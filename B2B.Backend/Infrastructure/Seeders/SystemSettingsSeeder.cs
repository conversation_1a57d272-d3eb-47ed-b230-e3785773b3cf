using Core.Entities;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Seeders;

public class SystemSettingsSeeder
{
    private readonly B2BDbContext _context;
    private readonly ILogger<SystemSettingsSeeder> _logger;

    public SystemSettingsSeeder(B2BDbContext context, ILogger<SystemSettingsSeeder> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task SeedAsync()
    {
        try
        {
            _logger.LogInformation("Starting SystemSettings seeding...");

            var defaultSettings = new List<SystemSettings>
            {
                new SystemSettings
                {
                    Id = Guid.CreateVersion7(),
                    Key = "PointValidationDays",
                    Value = "30",
                    Description = "Puanların geçerlilik süresi (gün). Bu süre sonunda beklemede olan puanlar kazanılır.",
                    Category = "Points",
                    DataType = "int",
                    IsSystem = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new SystemSettings
                {
                    Id = Guid.CreateVersion7(),
                    Key = "PointToTLRate",
                    Value = "1.0",
                    Description = "1 puanın TL karşılığı. Checkout'ta puan kullanımında kullanılır.",
                    Category = "Points",
                    DataType = "decimal",
                    IsSystem = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new SystemSettings
                {
                    Id = Guid.CreateVersion7(),
                    Key = "MaxPointUsagePercentage",
                    Value = "50",
                    Description = "Sepet tutarının maksimum yüzde kaçı puan ile ödenebilir.",
                    Category = "Points",
                    DataType = "int",
                    IsSystem = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new SystemSettings
                {
                    Id = Guid.CreateVersion7(),
                    Key = "PointSystemEnabled",
                    Value = "true",
                    Description = "Puan sistemi aktif mi?",
                    Category = "Points",
                    DataType = "bool",
                    IsSystem = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new SystemSettings
                {
                    Id = Guid.CreateVersion7(),
                    Key = "KayanYazi",
                    Value = "14 Gün İade Süresi, 1000 TL Üzeri Bedava Kargo, Hassas Ciltler İçin Rahatlama, Dermatolog Onaylı Ürünler",
                    Description = "Kayan yazı metni. Virgülle ayrılmış birden fazla metin girilebilir.",
                    Category = "Genel",
                    DataType = "string",
                    IsSystem = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            foreach (var setting in defaultSettings)
            {
                var existingSetting = await _context.SystemSettings
                    .Where(s => s.Key == setting.Key && !s.IsDeleted)
                    .FirstOrDefaultAsync();

                if (existingSetting == null)
                {
                    await _context.SystemSettings.AddAsync(setting);
                    _logger.LogInformation("Added system setting: {Key} = {Value}", setting.Key, setting.Value);
                }
                else
                {
                    _logger.LogDebug("System setting already exists: {Key}", setting.Key);
                }
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("SystemSettings seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while seeding SystemSettings");
            throw;
        }
    }
}
