using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repositories;

public class SystemSettingsRepository : GenericRepository<SystemSettings>, ISystemSettingsRepository
{
    private readonly B2BDbContext _dbContext;

    public SystemSettingsRepository(B2BDbContext context) : base(context)
    {
        _dbContext = context;
    }

    public async Task<SystemSettings?> GetByKeyAsync(string key)
    {
        return await _dbContext.SystemSettings
            .Where(s => s.Key == key && !s.IsDeleted)
            .FirstOrDefaultAsync();
    }

    public async Task<List<SystemSettings>> GetByCategoryAsync(string category)
    {
        return await _dbContext.SystemSettings
            .Where(s => s.Category == category && !s.IsDeleted)
            .OrderBy(s => s.Key)
            .ToListAsync();
    }

    public async Task<List<SystemSettings>> GetSystemSettingsAsync()
    {
        return await _dbContext.SystemSettings
            .Where(s => s.IsSystem && !s.IsDeleted)
            .OrderBy(s => s.Category)
            .ThenBy(s => s.Key)
            .ToListAsync();
    }

    public async Task<string?> GetValueAsync(string key)
    {
        var setting = await GetByKeyAsync(key);
        return setting?.Value;
    }

    public async Task<int?> GetIntValueAsync(string key)
    {
        var value = await GetValueAsync(key);
        if (string.IsNullOrEmpty(value))
            return null;

        return int.TryParse(value, out var result) ? result : null;
    }

    public async Task<decimal?> GetDecimalValueAsync(string key)
    {
        var value = await GetValueAsync(key);
        if (string.IsNullOrEmpty(value))
            return null;

        return decimal.TryParse(value, out var result) ? result : null;
    }

    public async Task<bool?> GetBoolValueAsync(string key)
    {
        var value = await GetValueAsync(key);
        if (string.IsNullOrEmpty(value))
            return null;

        return bool.TryParse(value, out var result) ? result : null;
    }

    public async Task<bool> UpdateValueAsync(string key, string value)
    {
        var setting = await GetByKeyAsync(key);
        if (setting == null)
            return false;

        setting.Value = value;
        setting.UpdatedAt = DateTime.UtcNow;

        _dbContext.SystemSettings.Update(setting);
        await _dbContext.SaveChangesAsync();

        return true;
    }

    public async Task AddToHistoryAsync(SystemSettings systemSettings, ChangeType changeType, Guid employeeId)
    {
        var history = new SystemSettingsHistory
        {
            Id = Guid.CreateVersion7(),
            Key = systemSettings.Key,
            Value = systemSettings.Value,
            Description = systemSettings.Description,
            Category = systemSettings.Category,
            DataType = systemSettings.DataType,
            IsSystem = systemSettings.IsSystem,
            ChangeType = changeType,
            EmployeeId = employeeId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _dbContext.SystemSettingsHistory.AddAsync(history);
    }
}
