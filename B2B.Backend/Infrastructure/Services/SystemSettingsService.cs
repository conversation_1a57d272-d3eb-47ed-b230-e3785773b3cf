using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class SystemSettingsService : ISystemSettingsService
{
    private readonly ISystemSettingsRepository _repository;

    public SystemSettingsService(ISystemSettingsRepository repository)
    {
        _repository = repository;
    }

    public async Task<List<SystemSettingsDto>> GetAllAsync()
    {
        var settings = await _repository.Query()
            .Where(s => !s.IsDeleted)
            .OrderBy(s => s.Category)
            .ThenBy(s => s.Key)
            .ToListAsync();

        return settings.Select(MapToDto).ToList();
    }

    public async Task<SystemSettingsDto?> GetByIdAsync(Guid id)
    {
        var setting = await _repository.GetByIdAsync(id);
        return setting != null ? MapToDto(setting) : null;
    }

    public async Task<SystemSettingsDto?> GetByKeyAsync(string key)
    {
        var setting = await _repository.GetByKeyAsync(key);
        return setting != null ? MapToDto(setting) : null;
    }

    public async Task<List<SystemSettingsDto>> GetByCategoryAsync(string category)
    {
        var settings = await _repository.GetByCategoryAsync(category);
        return settings.Select(MapToDto).ToList();
    }

    public async Task<List<SystemSettingsDto>> GetSystemSettingsAsync()
    {
        var settings = await _repository.GetSystemSettingsAsync();
        return settings.Select(MapToDto).ToList();
    }

    public async Task<Guid> CreateAsync(SystemSettingsCreateDto dto)
    {
        // Check if key already exists
        var existingSetting = await _repository.GetByKeyAsync(dto.Key);
        if (existingSetting != null)
            throw new InvalidOperationException($"Setting with key '{dto.Key}' already exists");

        var setting = new SystemSettings
        {
            Id = Guid.CreateVersion7(),
            Key = dto.Key,
            Value = dto.Value,
            Description = dto.Description,
            Category = dto.Category,
            DataType = dto.DataType,
            IsSystem = dto.IsSystem,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _repository.AddAsync(setting);
        await _repository.SaveChangesAsync();

        // Add to history
        await _repository.AddToHistoryAsync(setting, ChangeType.Created, Guid.Empty); // TODO: Get actual employee ID
        await _repository.SaveChangesAsync();

        return setting.Id;
    }

    public async Task UpdateAsync(SystemSettingsUpdateDto dto)
    {
        var setting = await _repository.GetByIdAsync(dto.Id);
        if (setting == null || setting.IsDeleted)
            throw new ArgumentException("Setting not found");

        setting.Value = dto.Value;
        setting.Description = dto.Description;
        setting.Category = dto.Category;
        setting.DataType = dto.DataType;
        setting.UpdatedAt = DateTime.UtcNow;

        _repository.Update(setting);
        await _repository.SaveChangesAsync();

        // Add to history
        await _repository.AddToHistoryAsync(setting, ChangeType.Updated, Guid.Empty); // TODO: Get actual employee ID
        await _repository.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var setting = await _repository.GetByIdAsync(id);
        if (setting == null || setting.IsDeleted)
            throw new ArgumentException("Setting not found");

        if (setting.IsSystem)
            throw new InvalidOperationException("System settings cannot be deleted");

        setting.IsDeleted = true;
        setting.UpdatedAt = DateTime.UtcNow;

        _repository.Update(setting);
        await _repository.SaveChangesAsync();

        // Add to history
        await _repository.AddToHistoryAsync(setting, ChangeType.Deleted, Guid.Empty); // TODO: Get actual employee ID
        await _repository.SaveChangesAsync();
    }

    public async Task<string?> GetValueAsync(string key)
    {
        return await _repository.GetValueAsync(key);
    }

    public async Task<int?> GetIntValueAsync(string key)
    {
        return await _repository.GetIntValueAsync(key);
    }

    public async Task<decimal?> GetDecimalValueAsync(string key)
    {
        return await _repository.GetDecimalValueAsync(key);
    }

    public async Task<bool?> GetBoolValueAsync(string key)
    {
        return await _repository.GetBoolValueAsync(key);
    }

    public async Task<bool> UpdateValueAsync(string key, string value)
    {
        return await _repository.UpdateValueAsync(key, value);
    }

    public async Task<int> GetPointValidationDaysAsync()
    {
        var days = await GetIntValueAsync("PointValidationDays");
        return days ?? 30; // Default 30 days
    }

    private static SystemSettingsDto MapToDto(SystemSettings setting)
    {
        return new SystemSettingsDto
        {
            Id = setting.Id,
            Key = setting.Key,
            Value = setting.Value,
            Description = setting.Description,
            Category = setting.Category,
            DataType = setting.DataType,
            IsSystem = setting.IsSystem,
            IsActive = setting.IsActive,
            IsDeleted = setting.IsDeleted,
            CreatedAt = setting.CreatedAt,
            UpdatedAt = setting.UpdatedAt
        };
    }
}
