using Application.Contracts.DTOs;
using Application.Contracts.Services;
using Core.Entities;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class ProductCategorySeoService : IProductCategorySeoService
{
    private readonly IGenericRepository<ProductCategorySeo> _repository;

    public ProductCategorySeoService(IGenericRepository<ProductCategorySeo> repository)
    {
        _repository = repository;
    }

    public async Task<ProductCategorySeoDto?> GetByIdAsync(Guid id)
    {
        try
        {
            var seo = await _repository.GetByIdAsync(id);
            if (seo == null) return null;

            return MapToDto(seo);
        }
        catch
        {
            return null;
        }
    }

    public async Task<ProductCategorySeoDto?> GetByCategoryIdAsync(Guid categoryId)
    {
        try
        {
            var seo = await _repository.FirstOrDefaultAsync(s => s.ProductCategoryId == categoryId);
            if (seo == null) return null;

            return MapToDto(seo);
        }
        catch
        {
            return null;
        }
    }

    public async Task<IEnumerable<ProductCategorySeoDto>> GetAllAsync()
    {
        try
        {
            var seos = await _repository.GetAllAsync();
            return seos.Select(MapToDto);
        }
        catch
        {
            return Enumerable.Empty<ProductCategorySeoDto>();
        }
    }

    public async Task<bool> CreateOrUpdateAsync(Guid categoryId, ProductCategorySeoCreateDto dto)
    {
        try
        {
            var existingSeo = await _repository.FirstOrDefaultAsync(s => s.ProductCategoryId == categoryId);

            if (existingSeo != null)
            {
                // Update existing
                existingSeo.MetaTitle = dto.MetaTitle;
                existingSeo.MetaDescription = dto.MetaDescription;
                existingSeo.MetaKeywords = dto.MetaKeywords;
                existingSeo.OgTitle = dto.OgTitle;
                existingSeo.OgDescription = dto.OgDescription;
                existingSeo.OgImage = dto.OgImage;
                existingSeo.StructuredData = dto.StructuredData;
                existingSeo.CanonicalUrl = dto.CanonicalUrl;
                existingSeo.NoIndex = dto.NoIndex;
                existingSeo.NoFollow = dto.NoFollow;

                _repository.Update(existingSeo);
            }
            else
            {
                // Create new
                var newSeo = new ProductCategorySeo
                {
                    ProductCategoryId = categoryId,
                    MetaTitle = dto.MetaTitle,
                    MetaDescription = dto.MetaDescription,
                    MetaKeywords = dto.MetaKeywords,
                    OgTitle = dto.OgTitle,
                    OgDescription = dto.OgDescription,
                    OgImage = dto.OgImage,
                    StructuredData = dto.StructuredData,
                    CanonicalUrl = dto.CanonicalUrl,
                    NoIndex = dto.NoIndex,
                    NoFollow = dto.NoFollow
                };

                await _repository.AddAsync(newSeo);
            }

            await _repository.SaveChangesAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> UpdateAsync(ProductCategorySeoUpdateDto dto)
    {
        try
        {
            var seo = await _repository.GetByIdAsync(dto.Id);
            if (seo == null) return false;

            seo.MetaTitle = dto.MetaTitle;
            seo.MetaDescription = dto.MetaDescription;
            seo.MetaKeywords = dto.MetaKeywords;
            seo.OgTitle = dto.OgTitle;
            seo.OgDescription = dto.OgDescription;
            seo.OgImage = dto.OgImage;
            seo.StructuredData = dto.StructuredData;
            seo.CanonicalUrl = dto.CanonicalUrl;
            seo.NoIndex = dto.NoIndex;
            seo.NoFollow = dto.NoFollow;

            _repository.Update(seo);
            await _repository.SaveChangesAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        try
        {
            var seo = await _repository.GetByIdAsync(id);
            if (seo == null) return false;

            _repository.Delete(seo);
            await _repository.SaveChangesAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> DeleteByCategoryIdAsync(Guid categoryId)
    {
        try
        {
            var seo = await _repository.FirstOrDefaultAsync(s => s.ProductCategoryId == categoryId);
            if (seo == null) return false;

            _repository.Delete(seo);
            await _repository.SaveChangesAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> GenerateAutoSeoAsync(Guid categoryId, string categoryName, string? description = null)
    {
        try
        {
            // Auto-generate SEO data based on category name and description
            var metaTitle = GenerateMetaTitle(categoryName);
            var metaDescription = GenerateMetaDescription(categoryName, description);
            var metaKeywords = GenerateMetaKeywords(categoryName, description);
            var ogTitle = metaTitle;
            var ogDescription = metaDescription;

            var seoDto = new ProductCategorySeoCreateDto
            {
                ProductCategoryId = categoryId,
                MetaTitle = metaTitle,
                MetaDescription = metaDescription,
                MetaKeywords = metaKeywords,
                OgTitle = ogTitle,
                OgDescription = ogDescription,
                NoIndex = false,
                NoFollow = false
            };

            return await CreateOrUpdateAsync(categoryId, seoDto);
        }
        catch
        {
            return false;
        }
    }

    private ProductCategorySeoDto MapToDto(ProductCategorySeo seo)
    {
        return new ProductCategorySeoDto
        {
            Id = seo.Id,
            ProductCategoryId = seo.ProductCategoryId,
            MetaTitle = seo.MetaTitle,
            MetaDescription = seo.MetaDescription,
            MetaKeywords = seo.MetaKeywords,
            OgTitle = seo.OgTitle,
            OgDescription = seo.OgDescription,
            OgImage = seo.OgImage,
            StructuredData = seo.StructuredData,
            CanonicalUrl = seo.CanonicalUrl,
            NoIndex = seo.NoIndex,
            NoFollow = seo.NoFollow,
            IsActive = seo.IsActive,
            CreatedAt = seo.CreatedAt,
            UpdatedAt = seo.UpdatedAt
        };
    }

    private string GenerateMetaTitle(string categoryName)
    {
        return $"{categoryName} Ürünleri";
    }

    private string GenerateMetaDescription(string categoryName, string? description = null)
    {
        if (!string.IsNullOrEmpty(description))
        {
            return description.Length > 160 ? description.Substring(0, 157) + "..." : description;
        }
        return $"{categoryName} kategorisindeki ürünleri keşfedin. Kaliteli ürünler, uygun fiyatlar ve hızlı teslimat.";
    }

    private string GenerateMetaKeywords(string categoryName, string? description = null)
    {
        var keywords = new List<string> { categoryName.ToLower(), "ürünler", "online alışveriş" };
        
        if (!string.IsNullOrEmpty(description))
        {
            // Extract potential keywords from description
            var words = description.Split(' ', StringSplitOptions.RemoveEmptyEntries)
                .Where(w => w.Length > 3)
                .Take(3)
                .Select(w => w.ToLower().Trim('.', ',', '!', '?'));
            keywords.AddRange(words);
        }

        return string.Join(", ", keywords.Distinct());
    }
}
