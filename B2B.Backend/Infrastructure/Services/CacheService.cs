using Application.Contracts.Services;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Infrastructure.Services;

/// <summary>
/// Memory cache service implementation
/// </summary>
public class CacheService : ICacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<CacheService> _logger;

    public CacheService(IMemoryCache memoryCache, ILogger<CacheService> logger)
    {
        _memoryCache = memoryCache;
        _logger = logger;
    }

    public async Task<T?> GetAsync<T>(string key) where T : class
    {
        try
        {
            if (_memoryCache.TryGetValue(key, out var cachedValue))
            {
                _logger.LogDebug("Cache HIT for key: {Key}", key);
                
                if (cachedValue is T directValue)
                {
                    return directValue;
                }
                
                if (cachedValue is string jsonValue)
                {
                    return JsonSerializer.Deserialize<T>(jsonValue);
                }
            }

            _logger.LogDebug("Cache MISS for key: {Key}", key);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache value for key: {Key}", key);
            return null;
        }
    }

    public async Task<bool> SetAsync<T>(string key, T value, TimeSpan expiration) where T : class
    {
        try
        {
            if (value == null)
            {
                _logger.LogWarning("Attempted to cache null value for key: {Key}", key);
                return false;
            }

            var cacheEntryOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiration,
                SlidingExpiration = null, // We want absolute expiration
                Priority = CacheItemPriority.Normal
            };

            // Store as JSON string for consistency
            var jsonValue = JsonSerializer.Serialize(value);
            _memoryCache.Set(key, jsonValue, cacheEntryOptions);

            _logger.LogDebug("Cache SET for key: {Key}, expiration: {Expiration}", key, expiration);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache value for key: {Key}", key);
            return false;
        }
    }

    public async Task<bool> RemoveAsync(string key)
    {
        try
        {
            _memoryCache.Remove(key);
            _logger.LogDebug("Cache REMOVE for key: {Key}", key);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache value for key: {Key}", key);
            return false;
        }
    }

    public async Task<bool> ExistsAsync(string key)
    {
        try
        {
            return _memoryCache.TryGetValue(key, out _);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache existence for key: {Key}", key);
            return false;
        }
    }

    public async Task<bool> ClearAsync()
    {
        try
        {
            // IMemoryCache doesn't have a clear method, so we need to dispose and recreate
            // This is not ideal but works for our use case
            if (_memoryCache is MemoryCache memoryCache)
            {
                memoryCache.Compact(1.0); // Remove all entries
            }
            
            _logger.LogDebug("Cache CLEAR executed");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache");
            return false;
        }
    }

    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, TimeSpan expiration) where T : class
    {
        try
        {
            // Try to get from cache first
            var cachedValue = await GetAsync<T>(key);
            if (cachedValue != null)
            {
                return cachedValue;
            }

            // If not in cache, use factory to create value
            _logger.LogDebug("Cache MISS for key: {Key}, executing factory", key);
            var newValue = await factory();
            
            if (newValue != null)
            {
                await SetAsync(key, newValue, expiration);
            }

            return newValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetOrSetAsync for key: {Key}", key);
            return null;
        }
    }
}
