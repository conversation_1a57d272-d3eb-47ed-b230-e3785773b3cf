﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateCouponSystemV2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Coupons_Customers_CustomerId",
                table: "Coupons");

            migrationBuilder.DropColumn(
                name: "IsSingleUse",
                table: "CouponsHistory");

            migrationBuilder.DropColumn(
                name: "IsUsed",
                table: "CouponsHistory");

            migrationBuilder.DropColumn(
                name: "IsSingleUse",
                table: "Coupons");

            migrationBuilder.DropColumn(
                name: "IsUsed",
                table: "Coupons");

            migrationBuilder.RenameColumn(
                name: "UsageLimit",
                table: "CouponsHistory",
                newName: "UsageLimitPerCustomer");

            migrationBuilder.RenameColumn(
                name: "UsageCount",
                table: "CouponsHistory",
                newName: "TotalUsageCount");

            migrationBuilder.RenameColumn(
                name: "UsageLimit",
                table: "Coupons",
                newName: "UsageLimitPerCustomer");

            migrationBuilder.RenameColumn(
                name: "UsageCount",
                table: "Coupons",
                newName: "TotalUsageCount");

            migrationBuilder.AlterColumn<Guid>(
                name: "CustomerId",
                table: "CouponsHistory",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddColumn<int>(
                name: "CouponType",
                table: "CouponsHistory",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "CouponsHistory",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MinimumCartAmount",
                table: "CouponsHistory",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "CouponsHistory",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TotalUsageLimit",
                table: "CouponsHistory",
                type: "integer",
                nullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "DiscountAmount",
                table: "Coupons",
                type: "numeric(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "numeric");

            migrationBuilder.AlterColumn<Guid>(
                name: "CustomerId",
                table: "Coupons",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<string>(
                name: "CouponCode",
                table: "Coupons",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddColumn<int>(
                name: "CouponType",
                table: "Coupons",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<Guid>(
                name: "CustomerId1",
                table: "Coupons",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "Coupons",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MinimumCartAmount",
                table: "Coupons",
                type: "numeric(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "Coupons",
                type: "character varying(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TotalUsageLimit",
                table: "Coupons",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "CouponUsages",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CouponId = table.Column<Guid>(type: "uuid", nullable: false),
                    CustomerId = table.Column<Guid>(type: "uuid", nullable: false),
                    OrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    UsageDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DiscountApplied = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    OrderAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    EmployeeId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CouponUsages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CouponUsages_AspNetUsers_EmployeeId",
                        column: x => x.EmployeeId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CouponUsages_Coupons_CouponId",
                        column: x => x.CouponId,
                        principalTable: "Coupons",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CouponUsages_Customers_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CouponUsages_Orders_OrderId",
                        column: x => x.OrderId,
                        principalTable: "Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CouponUsagesHistory",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CouponId = table.Column<Guid>(type: "uuid", nullable: false),
                    CustomerId = table.Column<Guid>(type: "uuid", nullable: false),
                    OrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    UsageDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DiscountApplied = table.Column<decimal>(type: "numeric", nullable: false),
                    OrderAmount = table.Column<decimal>(type: "numeric", nullable: false),
                    EntityId = table.Column<Guid>(type: "uuid", nullable: false),
                    ChangeType = table.Column<int>(type: "integer", nullable: false),
                    ChangeDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EmployeeId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CouponUsagesHistory", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Coupons_CouponCode",
                table: "Coupons",
                column: "CouponCode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Coupons_CouponType",
                table: "Coupons",
                column: "CouponType");

            migrationBuilder.CreateIndex(
                name: "IX_Coupons_CustomerId1",
                table: "Coupons",
                column: "CustomerId1");

            migrationBuilder.CreateIndex(
                name: "IX_Coupons_ExpirationDate",
                table: "Coupons",
                column: "ExpirationDate");

            migrationBuilder.CreateIndex(
                name: "IX_Coupons_IsActive",
                table: "Coupons",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_CouponUsages_CouponId",
                table: "CouponUsages",
                column: "CouponId");

            migrationBuilder.CreateIndex(
                name: "IX_CouponUsages_CouponId_CustomerId",
                table: "CouponUsages",
                columns: new[] { "CouponId", "CustomerId" });

            migrationBuilder.CreateIndex(
                name: "IX_CouponUsages_CustomerId",
                table: "CouponUsages",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_CouponUsages_EmployeeId",
                table: "CouponUsages",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_CouponUsages_OrderId",
                table: "CouponUsages",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_CouponUsages_UsageDate",
                table: "CouponUsages",
                column: "UsageDate");

            migrationBuilder.AddForeignKey(
                name: "FK_Coupons_Customers_CustomerId",
                table: "Coupons",
                column: "CustomerId",
                principalTable: "Customers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Coupons_Customers_CustomerId1",
                table: "Coupons",
                column: "CustomerId1",
                principalTable: "Customers",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Coupons_Customers_CustomerId",
                table: "Coupons");

            migrationBuilder.DropForeignKey(
                name: "FK_Coupons_Customers_CustomerId1",
                table: "Coupons");

            migrationBuilder.DropTable(
                name: "CouponUsages");

            migrationBuilder.DropTable(
                name: "CouponUsagesHistory");

            migrationBuilder.DropIndex(
                name: "IX_Coupons_CouponCode",
                table: "Coupons");

            migrationBuilder.DropIndex(
                name: "IX_Coupons_CouponType",
                table: "Coupons");

            migrationBuilder.DropIndex(
                name: "IX_Coupons_CustomerId1",
                table: "Coupons");

            migrationBuilder.DropIndex(
                name: "IX_Coupons_ExpirationDate",
                table: "Coupons");

            migrationBuilder.DropIndex(
                name: "IX_Coupons_IsActive",
                table: "Coupons");

            migrationBuilder.DropColumn(
                name: "CouponType",
                table: "CouponsHistory");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "CouponsHistory");

            migrationBuilder.DropColumn(
                name: "MinimumCartAmount",
                table: "CouponsHistory");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "CouponsHistory");

            migrationBuilder.DropColumn(
                name: "TotalUsageLimit",
                table: "CouponsHistory");

            migrationBuilder.DropColumn(
                name: "CouponType",
                table: "Coupons");

            migrationBuilder.DropColumn(
                name: "CustomerId1",
                table: "Coupons");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "Coupons");

            migrationBuilder.DropColumn(
                name: "MinimumCartAmount",
                table: "Coupons");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "Coupons");

            migrationBuilder.DropColumn(
                name: "TotalUsageLimit",
                table: "Coupons");

            migrationBuilder.RenameColumn(
                name: "UsageLimitPerCustomer",
                table: "CouponsHistory",
                newName: "UsageLimit");

            migrationBuilder.RenameColumn(
                name: "TotalUsageCount",
                table: "CouponsHistory",
                newName: "UsageCount");

            migrationBuilder.RenameColumn(
                name: "UsageLimitPerCustomer",
                table: "Coupons",
                newName: "UsageLimit");

            migrationBuilder.RenameColumn(
                name: "TotalUsageCount",
                table: "Coupons",
                newName: "UsageCount");

            migrationBuilder.AlterColumn<Guid>(
                name: "CustomerId",
                table: "CouponsHistory",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsSingleUse",
                table: "CouponsHistory",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsUsed",
                table: "CouponsHistory",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<decimal>(
                name: "DiscountAmount",
                table: "Coupons",
                type: "numeric",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "numeric(18,2)",
                oldPrecision: 18,
                oldScale: 2);

            migrationBuilder.AlterColumn<Guid>(
                name: "CustomerId",
                table: "Coupons",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CouponCode",
                table: "Coupons",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AddColumn<bool>(
                name: "IsSingleUse",
                table: "Coupons",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsUsed",
                table: "Coupons",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddForeignKey(
                name: "FK_Coupons_Customers_CustomerId",
                table: "Coupons",
                column: "CustomerId",
                principalTable: "Customers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
