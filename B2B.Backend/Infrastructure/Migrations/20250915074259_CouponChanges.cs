﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class CouponChanges : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Coupons_Customers_CustomerId1",
                table: "Coupons");

            migrationBuilder.DropIndex(
                name: "IX_Coupons_CustomerId1",
                table: "Coupons");

            migrationBuilder.DropColumn(
                name: "CustomerId1",
                table: "Coupons");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CustomerId1",
                table: "Coupons",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Coupons_CustomerId1",
                table: "Coupons",
                column: "CustomerId1");

            migrationBuilder.AddForeignKey(
                name: "FK_Coupons_Customers_CustomerId1",
                table: "Coupons",
                column: "CustomerId1",
                principalTable: "Customers",
                principalColumn: "Id");
        }
    }
}
