using Shipping.Abstraction;
using Shipping.Implementation;
using Shipping.YurticiKargo;
using Shipping.YurticiKargoTest;

using Application.Contracts.Interfaces;
using Application.Contracts.Repositories;
using Application.Contracts.Services;
using Infrastructure.Services;
using Infrastructure.Repositories;

namespace WorkerService.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services)
    {
        services.AddScoped<IProductAttributeService, ProductAttributeService>();
        services.AddScoped<IProductImageService, ProductImageService>();
        services.AddScoped<IProductVolumeService, ProductVolumeService>();

        // Campaign Services
        services.AddScoped<ICampaignRepository, CampaignRepository>();
        services.AddScoped<ICampaignService, CampaignService>();
        services.AddScoped<ICampaignCalculationService, CampaignCalculationService>();

        // Pricing Services
        services.AddScoped<IPricingCalculationService, PricingCalculationService>();

        return services;
    }

    public static IServiceCollection AddModules(this IServiceCollection services)
    {
        // Shipping factory pattern
        services.AddScoped<IShippingServiceFactory, ShippingServiceFactory>();
        services.AddHttpContextAccessor();

        services.AddScoped<ICurrentUserService, CurrentUserService>();
        // Shipping modules
        services.AddYurticiKargoShipping();
        services.AddYurticiKargoTestShipping();

        services.AddScoped<IPaymentProviderRepository, PaymentProviderRepository>();
        services.AddScoped<IPaymentProviderService, PaymentProviderService>();

        // System Settings
        services.AddScoped<Core.Interfaces.ISystemSettingsRepository, Infrastructure.Repositories.SystemSettingsRepository>();
        services.AddScoped<ISystemSettingsService, Infrastructure.Services.SystemSettingsService>();

        // User Points
        services.AddScoped<Core.Interfaces.IUserPointRepository, Infrastructure.Repositories.UserPointRepository>();
        services.AddScoped<IUserPointService, Infrastructure.Services.UserPointService>();

        return services;
    }
}
