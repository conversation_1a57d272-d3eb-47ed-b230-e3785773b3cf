# Build aşaması
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy csproj files and restore dependencies
COPY ["WorkerService/WorkerService.csproj", "WorkerService/"]
COPY ["Core/Core.csproj", "Core/"]
COPY ["Infrastructure/Infrastructure.csproj", "Infrastructure/"]
COPY ["Application.Contracts/Application.Contracts.csproj", "Application.Contracts/"]
COPY ["Modules/Shipping.Abstraction/Shipping.Abstraction.csproj", "Modules/Shipping.Abstraction/"]
COPY ["Modules/Shipping.Implementation/Shipping.Implementation.csproj", "Modules/Shipping.Implementation/"]
COPY ["Modules/Shipping.YurticiKargo/Shipping.YurticiKargo.csproj", "Modules/Shipping.YurticiKargo/"]
COPY ["Modules/Shipping.YurticiKargoTest/Shipping.YurticiKargoTest.csproj", "Modules/Shipping.YurticiKargoTest/"]

RUN dotnet restore "WorkerService/WorkerService.csproj"

# Copy everything else and build
COPY . .
WORKDIR "/src/WorkerService"
RUN dotnet build "WorkerService.csproj" -c Release -o /app/build

# Publish aşaması
FROM build AS publish
RUN dotnet publish "WorkerService.csproj" -c Release -o /app/publish

# Runtime aşaması
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
WORKDIR /app
COPY --from=publish /app/publish .

EXPOSE 5000
ENTRYPOINT ["dotnet", "WorkerService.dll"]
