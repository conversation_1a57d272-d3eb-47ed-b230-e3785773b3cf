using Core.Entities;
using Core.Enums;
using Core.Events;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Shipping.Abstraction;
using Shipping.Abstraction.Models;
using Shipping.Implementation;
using MassTransit;

namespace WorkerService.Services;

/// <summary>
/// Kargo takip background service'i
/// 10 dakikada bir çalışarak kargo durumlarını kontrol eder
/// </summary>
public class ShipmentTrackingWorker : BackgroundService
{
    private readonly ILogger<ShipmentTrackingWorker> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly TimeSpan _interval;
    private readonly int _batchSize;

    public ShipmentTrackingWorker(
        ILogger<ShipmentTrackingWorker> logger,
        IServiceProvider serviceProvider,
        IConfiguration configuration,
        IPublishEndpoint publishEndpoint)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _configuration = configuration;
        _publishEndpoint = publishEndpoint;

        // Konfigürasyondan ayarları al
        _interval = TimeSpan.FromMinutes(_configuration.GetValue<int>("ShipmentTracking:IntervalMinutes", 10));
        _batchSize = _configuration.GetValue<int>("ShipmentTracking:BatchSize", 50);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Shipment Tracking Worker started. Interval: {Interval} minutes", _interval.TotalMinutes);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessShipmentTrackingAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while processing shipment tracking");
            }

            // Belirtilen süre kadar bekle
            await Task.Delay(_interval, stoppingToken);
        }
    }

    private async Task ProcessShipmentTrackingAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<B2BDbContext>();
        var shippingServiceFactory = scope.ServiceProvider.GetRequiredService<IShippingServiceFactory>();

        try
        {
            // CargoKey'i olan ve henüz TrackingNumber'ı olmayan kargolar
            var shipmentsToTrack = await dbContext.Shipments
                .Include(s => s.Carrier)
                .Include(s => s.Order)
                    .ThenInclude(o => o.Customer)
                .Include(s => s.Order)
                    .ThenInclude(o => o.Address)
                .Where(s => s.CargoKey != null &&
                           s.TrackingNumber == null &&
                           s.Status == ShipmentStatus.Shipped &&
                           s.Carrier.IsImplemented)
                .Distinct()
                .OrderBy(s => s.Carrier.ShortCode)
                .OrderBy(s => s.CreatedAt)
                .Take(_batchSize)
                .ToListAsync(cancellationToken);

            if (!shipmentsToTrack.Any())
            {
                _logger.LogDebug("No shipments found for tracking");
                return;
            }

            _logger.LogInformation("Processing {Count} shipments for tracking", shipmentsToTrack.Count);

            // ShipmentCarrier'e göre grupla
            var shipmentGroups = shipmentsToTrack
                .GroupBy(s => s.Carrier.ShortCode)
                .ToList();

            foreach (var group in shipmentGroups)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                await ProcessBatchShipmentAsync([.. group], shippingServiceFactory, dbContext, cancellationToken);
            }

            await dbContext.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing shipment tracking batch");
        }
    }

    private async Task ProcessBatchShipmentAsync(
        List<Shipment> shipments,
        IShippingServiceFactory shippingServiceFactory,
        B2BDbContext dbContext,
        CancellationToken cancellationToken)
    {
        if (shipments.Count == 0)
            return;

        var carrierCode = shipments[0].Carrier.ShortCode;
        var cargoKeys = shipments.Select(s => s.CargoKey!).ToArray();

        try
        {
            _logger.LogDebug("Processing batch of {Count} shipments for carrier {CarrierCode} with CargoKeys: {CargoKeys}",
                shipments.Count, carrierCode, string.Join(", ", cargoKeys));

            // Kargo servisini al
            var shippingService = shippingServiceFactory.GetService(carrierCode);
            if (shippingService == null)
            {
                _logger.LogWarning("Shipping service not found for carrier {CarrierCode}", carrierCode);
                return;
            }

            // Batch olarak kargo durumunu sorgula
            var queryResult = await shippingService.GetTrackingUrlAsync(cargoKeys);

            if (queryResult.IsSuccess)
            {
                // Batch sonucunu tek tek shipment'lara uygula
                foreach (var shipment in shipments)
                {
                    await ProcessSingleShipmentResult(shipment, queryResult);
                }

                _logger.LogInformation("Successfully processed batch of {Count} shipments for carrier {CarrierCode}",
                    shipments.Count, carrierCode);
            }
            else
            {
                _logger.LogWarning("Failed to query batch shipments for carrier {CarrierCode}: {Error}",
                    carrierCode, queryResult.ErrorMessage);

                // Batch başarısız olursa tek tek dene
                _logger.LogInformation("Falling back to individual processing for carrier {CarrierCode}", carrierCode);
                foreach (var shipment in shipments)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    await ProcessSingleShipmentAsync(shipment, shippingServiceFactory, dbContext, cancellationToken);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing batch shipments for carrier {CarrierCode}", carrierCode);

            // Hata durumunda tek tek dene
            _logger.LogInformation("Falling back to individual processing due to error for carrier {CarrierCode}", carrierCode);
            foreach (var shipment in shipments)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                await ProcessSingleShipmentAsync(shipment, shippingServiceFactory, dbContext, cancellationToken);
            }
        }
    }

    private async Task ProcessSingleShipmentResult(
        Shipment shipment,
        ShipmentQueryResult queryResult)
    {
        try
        {
            bool updated = false;

            // TrackingUrl varsa TrackingNumber'ı güncelle
            if (!string.IsNullOrEmpty(queryResult.TrackingUrl) &&
                shipment.TrackingNumber != queryResult.TrackingUrl)
            {
                shipment.TrackingNumber = queryResult.TrackingNumber;
                updated = true;
                _logger.LogInformation("Updated TrackingNumber for shipment {ShipmentId}: {TrackingUrl}",
                    shipment.Id, queryResult.TrackingUrl);
            }

            // Kargo işlem gördüyse durumu güncelle
            if (queryResult.IsProcessed && shipment.Status == ShipmentStatus.Shipped)
            {
                shipment.Status = ShipmentStatus.InTransit;
                updated = true;
                _logger.LogInformation("Updated shipment {ShipmentId} status to InTransit", shipment.Id);
            }

            // Notları güncelle
            if (!string.IsNullOrEmpty(queryResult.Notes))
            {
                shipment.Notes = queryResult.Notes;
                updated = true;
            }

            if (updated)
            {
                shipment.UpdatedAt = DateTime.UtcNow;

                // TrackingUrl geldi ve uyarı yoksa mail gönder
                if (!string.IsNullOrEmpty(queryResult.TrackingUrl) &&
                    string.IsNullOrEmpty(queryResult.ErrorMessage))
                {
                    await PublishShipmentTrackingNotificationAsync(shipment, queryResult.TrackingUrl);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing single shipment result for {ShipmentId}", shipment.Id);
        }
    }

    private async Task ProcessSingleShipmentAsync(
        Shipment shipment,
        IShippingServiceFactory shippingServiceFactory,
        B2BDbContext dbContext,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Processing shipment {ShipmentId} with CargoKey {CargoKey}",
                shipment.Id, shipment.CargoKey);

            // Kargo servisini al
            var shippingService = shippingServiceFactory.GetService(shipment.Carrier.ShortCode);
            if (shippingService == null)
            {
                _logger.LogWarning("Shipping service not found for carrier {CarrierCode}", shipment.Carrier.ShortCode);
                return;
            }

            // Kargo durumunu sorgula (tek shipment için array oluştur)
            var queryResult = await shippingService.GetTrackingUrlAsync([shipment.CargoKey!]);

            if (queryResult.IsSuccess)
            {
                await ProcessSingleShipmentResult(shipment, queryResult);
            }
            else
            {
                _logger.LogWarning("Failed to query shipment {ShipmentId}: {Error}",
                    shipment.Id, queryResult.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing shipment {ShipmentId}", shipment.Id);
        }
    }

    private async Task PublishShipmentTrackingNotificationAsync(Shipment shipment, string trackingUrl)
    {
        try
        {
            var notificationEvent = new CustomerShipmentNotificationRequested
            {
                ShipmentId = shipment.Id,
                OrderId = shipment.OrderId,
                CustomerEmail = shipment.Order?.Customer?.Email ?? string.Empty,
                CustomerName = shipment.Order?.Customer?.NameSurname ?? "Müşteri",
                TrackingNumber = shipment.TrackingNumber ?? string.Empty,
                TrackingUrl = trackingUrl,
                CarrierName = shipment.Carrier?.Name ?? "Kargo Firması",
                OrderNumber = shipment.Order?.OrderNumber ?? shipment.OrderId.ToString(),
                DeliveryAddress = shipment.Order?.Address?.FullAddress ?? "Teslimat Adresi",
                EstimatedDeliveryDate = DateTime.UtcNow.AddDays(2), // Varsayılan 2 gün
                CreatedAt = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(notificationEvent);

            _logger.LogInformation("Published shipment tracking notification for Shipment {ShipmentId}, Customer {CustomerEmail}",
                shipment.Id, notificationEvent.CustomerEmail);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing shipment tracking notification for Shipment {ShipmentId}", shipment.Id);
        }
    }
}
