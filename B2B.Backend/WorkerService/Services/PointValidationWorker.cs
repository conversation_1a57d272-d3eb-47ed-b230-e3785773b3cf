using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace WorkerService.Services;

/// <summary>
/// Puan geçerlilik kontrolü background service'i
/// Her gün başlangıcında çalışarak beklemede olan puanları kontrol eder
/// Geçerlilik süresi dolan puanları kazandırır
/// </summary>
public class PointValidationWorker : BackgroundService
{
    private readonly ILogger<PointValidationWorker> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly TimeSpan _interval;

    public PointValidationWorker(
        ILogger<PointValidationWorker> logger,
        IServiceProvider serviceProvider,
        IConfiguration configuration)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        
        // Her gün saat 00:01'de çalışacak şekilde ayarla
        var intervalHours = configuration.GetValue<int>("PointValidation:IntervalHours", 24);
        _interval = TimeSpan.FromHours(intervalHours);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Point Validation Worker started. Interval: {Interval} hours", _interval.TotalHours);

        // İlk çalıştırmayı bir sonraki gece yarısına ayarla
        await WaitUntilMidnight(stoppingToken);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessPointValidationAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while processing point validation");
            }

            // Bir sonraki çalıştırmaya kadar bekle
            await Task.Delay(_interval, stoppingToken);
        }
    }

    private async Task WaitUntilMidnight(CancellationToken stoppingToken)
    {
        var now = DateTime.Now;
        var nextMidnight = now.Date.AddDays(1).AddMinutes(1); // 00:01
        var delay = nextMidnight - now;

        if (delay.TotalMilliseconds > 0)
        {
            _logger.LogInformation("Waiting until {NextRun} to start point validation", nextMidnight);
            await Task.Delay(delay, stoppingToken);
        }
    }

    private async Task ProcessPointValidationAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<B2BDbContext>();
        var systemSettingsService = scope.ServiceProvider.GetRequiredService<ISystemSettingsService>();
        var userPointService = scope.ServiceProvider.GetRequiredService<IUserPointService>();

        try
        {
            _logger.LogInformation("Starting point validation process");

            // Puan geçerlilik gün sayısını al
            var validationDays = await systemSettingsService.GetPointValidationDaysAsync();
            var validationDate = DateTime.UtcNow.AddDays(-validationDays);

            _logger.LogInformation("Processing points older than {ValidationDate} (validation days: {ValidationDays})", 
                validationDate, validationDays);

            // Geçerlilik süresi dolan beklemede olan puanları getir
            var pendingPoints = await dbContext.UserPoints
                .Include(up => up.Customer)
                .Include(up => up.Order)
                .Where(up => up.Status == PointStatus.Pending &&
                            !up.IsDeleted &&
                            up.CreatedAt <= validationDate)
                .OrderBy(up => up.CreatedAt)
                .ToListAsync(cancellationToken);

            if (!pendingPoints.Any())
            {
                _logger.LogInformation("No pending points found for validation");
                return;
            }

            _logger.LogInformation("Found {Count} pending points to validate", pendingPoints.Count);

            var processedCount = 0;
            var earnedCount = 0;

            foreach (var pendingPoint in pendingPoints)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    // İlgili siparişin durumunu kontrol et (eğer sipariş iptal edilmişse puanı iptal et)
                    var isOrderValid = await IsOrderValidForPoints(dbContext, pendingPoint, cancellationToken);

                    if (isOrderValid)
                    {
                        // Puanı kazandır
                        var success = await userPointService.EarnPendingPointsAsync(pendingPoint.Id);
                        if (success)
                        {
                            earnedCount++;
                            _logger.LogDebug("Earned {Points} points for customer {CustomerId}", 
                                pendingPoint.Point, pendingPoint.CustomerId);
                        }
                    }
                    else
                    {
                        // Puanı iptal et
                        await userPointService.CancelPendingPointsAsync(pendingPoint.Id);
                        _logger.LogDebug("Cancelled {Points} points for customer {CustomerId} due to invalid order", 
                            pendingPoint.Point, pendingPoint.CustomerId);
                    }

                    processedCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing pending point {PointId} for customer {CustomerId}", 
                        pendingPoint.Id, pendingPoint.CustomerId);
                }
            }

            _logger.LogInformation("Point validation completed. Processed: {ProcessedCount}, Earned: {EarnedCount}, Cancelled: {CancelledCount}", 
                processedCount, earnedCount, processedCount - earnedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during point validation process");
        }
    }

    private async Task<bool> IsOrderValidForPoints(B2BDbContext dbContext, UserPoint pendingPoint, CancellationToken cancellationToken)
    {
        // Eğer OrderId varsa, direkt sipariş kontrolü yap
        if (pendingPoint.OrderId.HasValue)
        {
            // Eğer Order zaten include edilmişse, veritabanına tekrar sorgu yapmaya gerek yok
            if (pendingPoint.Order != null)
            {
                // Sipariş iptal edilmişse veya iade edilmişse puan geçersiz
                return pendingPoint.Order.Status != OrderStatus.Cancelled && pendingPoint.Order.Status != OrderStatus.Refunded;
            }

            // Order include edilmemişse veritabanından getir
            var order = await dbContext.Orders
                .Where(o => o.Id == pendingPoint.OrderId.Value && !o.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (order != null)
            {
                // Sipariş iptal edilmişse veya iade edilmişse puan geçersiz
                return order.Status != OrderStatus.Cancelled && order.Status != OrderStatus.Refunded;
            }

            // OrderId varsa ama sipariş bulunamazsa puan geçersiz
            return false;
        }

        // Geriye dönük uyumluluk için açıklamadan sipariş numarası çıkarmaya çalış
        if (pendingPoint.Description.Contains("Sipariş #"))
        {
            try
            {
                var orderNumberStart = pendingPoint.Description.IndexOf("#") + 1;
                var orderNumberEnd = pendingPoint.Description.IndexOf(" ", orderNumberStart);
                if (orderNumberEnd == -1) orderNumberEnd = pendingPoint.Description.Length;

                var orderNumber = pendingPoint.Description.Substring(orderNumberStart, orderNumberEnd - orderNumberStart);

                var order = await dbContext.Orders
                    .Where(o => o.OrderNumber == orderNumber && !o.IsDeleted)
                    .FirstOrDefaultAsync(cancellationToken);

                if (order != null)
                {
                    // Sipariş iptal edilmişse veya iade edilmişse puan geçersiz
                    return order.Status != OrderStatus.Cancelled && order.Status != OrderStatus.Refunded;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not extract order number from point description: {Description}",
                    pendingPoint.Description);
            }
        }

        // Sipariş bulunamazsa veya açıklama formatı farklıysa, puanı geçerli say
        return true;
    }
}
