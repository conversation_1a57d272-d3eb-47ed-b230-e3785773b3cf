using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

[ApiController]
[Route("api/customer")]
[EnableCors("AllowCustomerFrontend")]
public class CustomerAuthController : ControllerBase
{
    private readonly ICustomerAuthService _customerAuthService;
    private readonly IUserPointService _userPointService;

    public CustomerAuthController(
        ICustomerAuthService customerAuthService,
        IUserPointService userPointService)
    {
        _customerAuthService = customerAuthService;
        _userPointService = userPointService;
    }

    /// <summary>
    /// Customer login endpoint
    /// </summary>
    /// <param name="dto">Login credentials</param>
    /// <returns>JWT token and customer information</returns>
    [AllowAnonymous]
    [HttpPost("login")]
    public async Task<ActionResult<ApiResponse<CustomerAuthResponseDto>>> Login([FromBody] CustomerLoginDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<CustomerAuthResponseDto>.ErrorResponse(
                "Geçersiz veri.", 400,
                string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))));
        }

        try
        {
            var result = await _customerAuthService.LoginAsync(dto);

            if (result.IsSuccessful)
            {
                return Ok(ApiResponse<CustomerAuthResponseDto>.SuccessResponse(result, "Giriş başarılı"));
            }

            return Unauthorized(ApiResponse<CustomerAuthResponseDto>.ErrorResponse(result.Message, 401));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CustomerAuthResponseDto>.ErrorResponse(
                "Giriş sırasında bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Customer registration endpoint
    /// </summary>
    /// <param name="dto">Registration data</param>
    /// <returns>JWT token and customer information</returns>
    [HttpPost("register")]
    public async Task<ActionResult<ApiResponse<CustomerAuthResponseDto>>> Register([FromBody] CustomerRegisterDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<CustomerAuthResponseDto>.ErrorResponse(
                "Geçersiz veri.", 400,
                string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))));
        }

        try
        {
            var result = await _customerAuthService.RegisterAsync(dto);

            if (result.IsSuccessful)
            {
                return Ok(ApiResponse<CustomerAuthResponseDto>.SuccessResponse(result, "Kayıt başarılı"));
            }

            return BadRequest(ApiResponse<CustomerAuthResponseDto>.ErrorResponse(result.Message, 400));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CustomerAuthResponseDto>.ErrorResponse(
                "Kayıt sırasında bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Validate customer token endpoint
    /// </summary>
    /// <returns>Customer information if token is valid</returns>
    [HttpGet("validate")]
    public async Task<ActionResult<ApiResponse<CustomerDto>>> ValidateToken()
    {
        try
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return Unauthorized(ApiResponse<CustomerDto>.ErrorResponse("Token bulunamadı.", 401));
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            var customer = await _customerAuthService.ValidateTokenAsync(token);

            if (customer == null)
            {
                return Unauthorized(ApiResponse<CustomerDto>.ErrorResponse("Geçersiz token.", 401));
            }

            return Ok(ApiResponse<CustomerDto>.SuccessResponse(customer, "Token geçerli"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CustomerDto>.ErrorResponse(
                "Token doğrulama sırasında bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Get customer profile endpoint (requires authentication)
    /// </summary>
    /// <returns>Customer profile information</returns>
    [HttpGet("profile")]
    public async Task<ActionResult<ApiResponse<CustomerDto>>> GetProfile()
    {
        try
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return Unauthorized(ApiResponse<CustomerDto>.ErrorResponse("Token bulunamadı.", 401));
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            var customer = await _customerAuthService.ValidateTokenAsync(token);

            if (customer == null)
            {
                return Unauthorized(ApiResponse<CustomerDto>.ErrorResponse("Geçersiz token.", 401));
            }

            return Ok(ApiResponse<CustomerDto>.SuccessResponse(customer, "Profil bilgileri getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CustomerDto>.ErrorResponse(
                "Profil bilgileri getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Request password reset for customer
    /// </summary>
    /// <param name="email">Customer email</param>
    /// <returns>Response indicating success</returns>
    [AllowAnonymous]
    [HttpPost("request-password-reset")]
    public async Task<ActionResult<ApiResponse<CustomerAuthResponseDto>>> RequestPasswordReset([FromBody] string email)
    {
        if (string.IsNullOrEmpty(email))
        {
            return BadRequest(ApiResponse<CustomerAuthResponseDto>.ErrorResponse("Email adresi gerekli.", 400));
        }

        try
        {
            var result = await _customerAuthService.RequestPasswordResetAsync(email);
            return Ok(ApiResponse<CustomerAuthResponseDto>.SuccessResponse(result, result.Message));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CustomerAuthResponseDto>.ErrorResponse(
                "Şifre sıfırlama talebinde bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Reset customer password using token
    /// </summary>
    /// <param name="dto">Reset password data</param>
    /// <returns>Response indicating success</returns>
    [AllowAnonymous]
    [HttpPost("reset-password")]
    public async Task<ActionResult<ApiResponse<CustomerAuthResponseDto>>> ResetPassword([FromBody] CustomerPasswordResetDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<CustomerAuthResponseDto>.ErrorResponse(
                "Geçersiz veri.", 400,
                string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))));
        }

        try
        {
            var result = await _customerAuthService.ResetPasswordAsync(dto.Token, dto.NewPassword);

            if (result.IsSuccessful)
            {
                return Ok(ApiResponse<CustomerAuthResponseDto>.SuccessResponse(result, result.Message));
            }

            return BadRequest(ApiResponse<CustomerAuthResponseDto>.ErrorResponse(result.Message, 400));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CustomerAuthResponseDto>.ErrorResponse(
                "Şifre sıfırlama sırasında bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Update customer profile endpoint (requires authentication)
    /// </summary>
    /// <param name="dto">Profile update data</param>
    /// <returns>Updated customer profile information</returns>
    [HttpPut("profile")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<CustomerDto>>> UpdateProfile([FromBody] CustomerProfileUpdateDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<CustomerDto>.ErrorResponse(
                "Geçersiz veri.", 400,
                string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))));
        }

        try
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return Unauthorized(ApiResponse<CustomerDto>.ErrorResponse("Token bulunamadı.", 401));
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            var customer = await _customerAuthService.ValidateTokenAsync(token);

            if (customer == null)
            {
                return Unauthorized(ApiResponse<CustomerDto>.ErrorResponse("Geçersiz token.", 401));
            }

            // Ensure the customer can only update their own profile
            if (customer.Id != dto.Id)
            {
                return Forbid();
            }

            var result = await _customerAuthService.UpdateProfileAsync(dto);

            if (result.IsSuccessful)
            {
                return Ok(ApiResponse<CustomerDto>.SuccessResponse(result.Customer!, result.Message));
            }

            return BadRequest(ApiResponse<CustomerDto>.ErrorResponse(result.Message, 400));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CustomerDto>.ErrorResponse(
                "Profil güncellenirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Update customer password endpoint (requires authentication)
    /// </summary>
    /// <param name="dto">Password update data</param>
    /// <returns>Response indicating success</returns>
    [HttpPut("password")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<object>>> UpdatePassword([FromBody] CustomerPasswordUpdateDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<object>.ErrorResponse(
                "Geçersiz veri.", 400,
                string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))));
        }

        try
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return Unauthorized(ApiResponse<object>.ErrorResponse("Token bulunamadı.", 401));
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            var customer = await _customerAuthService.ValidateTokenAsync(token);

            if (customer == null)
            {
                return Unauthorized(ApiResponse<object>.ErrorResponse("Geçersiz token.", 401));
            }

            // Ensure the customer can only update their own password
            if (customer.Id != dto.CustomerId)
            {
                return Forbid();
            }

            var result = await _customerAuthService.UpdatePasswordAsync(dto);

            if (result.IsSuccessful)
            {
                return Ok(ApiResponse<object>.SuccessResponse(new { }, result.Message));
            }

            return BadRequest(ApiResponse<object>.ErrorResponse(result.Message, 400));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<object>.ErrorResponse(
                "Şifre güncellenirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Deactivate customer account endpoint (requires authentication)
    /// </summary>
    /// <param name="dto">Deactivation data</param>
    /// <returns>Response indicating success</returns>
    [HttpPost("deactivate")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<object>>> DeactivateAccount([FromBody] CustomerDeactivateDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<object>.ErrorResponse(
                "Geçersiz veri.", 400,
                string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))));
        }

        try
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return Unauthorized(ApiResponse<object>.ErrorResponse("Token bulunamadı.", 401));
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            var customer = await _customerAuthService.ValidateTokenAsync(token);

            if (customer == null)
            {
                return Unauthorized(ApiResponse<object>.ErrorResponse("Geçersiz token.", 401));
            }

            // Ensure the customer can only deactivate their own account
            if (customer.Id != dto.CustomerId)
            {
                return Forbid();
            }

            var result = await _customerAuthService.DeactivateAccountAsync(dto);

            if (result.IsSuccessful)
            {
                return Ok(ApiResponse<object>.SuccessResponse(new { }, result.Message));
            }

            return BadRequest(ApiResponse<object>.ErrorResponse(result.Message, 400));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<object>.ErrorResponse(
                "Hesap pasifleştirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Get customer points balance endpoint (requires authentication)
    /// </summary>
    /// <returns>Customer current points balance</returns>
    [HttpGet("points/balance")]
    public async Task<ActionResult<ApiResponse<decimal>>> GetPointsBalance()
    {
        try
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return Unauthorized(ApiResponse<decimal>.ErrorResponse("Token bulunamadı.", 401));
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            var customer = await _customerAuthService.ValidateTokenAsync(token);

            if (customer == null)
            {
                return Unauthorized(ApiResponse<decimal>.ErrorResponse("Geçersiz token.", 401));
            }

            var balance = await _userPointService.GetCustomerCurrentBalanceAsync(customer.Id);
            return Ok(ApiResponse<decimal>.SuccessResponse(balance, "Puan bakiyesi getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<decimal>.ErrorResponse(
                "Puan bakiyesi getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }
}
