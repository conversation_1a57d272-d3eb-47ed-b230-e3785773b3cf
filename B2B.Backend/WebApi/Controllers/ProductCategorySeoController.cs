using Application.Contracts.DTOs;
using Application.Contracts.Services;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

[ApiController]
[Route("web-api/[controller]")]
public class ProductCategorySeoController : ControllerBase
{
    private readonly IProductCategorySeoService _productCategorySeoService;

    public ProductCategorySeoController(IProductCategorySeoService productCategorySeoService)
    {
        _productCategorySeoService = productCategorySeoService;
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<ProductCategorySeoDto>>> GetById(Guid id)
    {
        try
        {
            var seo = await _productCategorySeoService.GetByIdAsync(id);
            if (seo == null)
            {
                return Ok(ApiResponse<ProductCategorySeoDto>.NotFoundResponse("SEO bilgisi bulunamadı."));
            }

            return Ok(ApiResponse<ProductCategorySeoDto>.SuccessResponse(seo));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<ProductCategorySeoDto>.ErrorResponse($"Sunucu hatası: {ex.Message}"));
        }
    }

    [HttpGet("category/{categoryId}")]
    public async Task<ActionResult<ApiResponse<ProductCategorySeoDto>>> GetByCategoryId(Guid categoryId)
    {
        try
        {
            var seo = await _productCategorySeoService.GetByCategoryIdAsync(categoryId);
            if (seo == null)
            {
                return Ok(ApiResponse<ProductCategorySeoDto>.NotFoundResponse("Kategori için SEO bilgisi bulunamadı."));
            }

            return Ok(ApiResponse<ProductCategorySeoDto>.SuccessResponse(seo));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<ProductCategorySeoDto>.ErrorResponse($"Sunucu hatası: {ex.Message}"));
        }
    }

    [HttpGet]
    public async Task<ActionResult<ApiResponse<IEnumerable<ProductCategorySeoDto>>>> GetAll()
    {
        try
        {
            var seos = await _productCategorySeoService.GetAllAsync();
            return Ok(ApiResponse<IEnumerable<ProductCategorySeoDto>>.SuccessResponse(seos));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<IEnumerable<ProductCategorySeoDto>>.ErrorResponse($"Sunucu hatası: {ex.Message}"));
        }
    }

    [HttpPost("category/{categoryId}")]
    public async Task<ActionResult<ApiResponse<bool>>> CreateOrUpdate(Guid categoryId, [FromBody] ProductCategorySeoCreateDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<bool>.BadRequestResponse("Geçersiz veri."));
            }

            dto.ProductCategoryId = categoryId;
            var result = await _productCategorySeoService.CreateOrUpdateAsync(categoryId, dto);

            if (result)
            {
                return Ok(ApiResponse<bool>.SuccessResponse(true, "SEO bilgisi başarıyla kaydedildi."));
            }

            return BadRequest(ApiResponse<bool>.BadRequestResponse("SEO bilgisi kaydedilemedi."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<bool>.ErrorResponse($"Sunucu hatası: {ex.Message}"));
        }
    }

    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<bool>>> Update(Guid id, [FromBody] ProductCategorySeoUpdateDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<bool>.BadRequestResponse("Geçersiz veri."));
            }

            if (id != dto.Id)
            {
                return BadRequest(ApiResponse<bool>.BadRequestResponse("ID uyuşmazlığı."));
            }

            var result = await _productCategorySeoService.UpdateAsync(dto);

            if (result)
            {
                return Ok(ApiResponse<bool>.SuccessResponse(true, "SEO bilgisi başarıyla güncellendi."));
            }

            return BadRequest(ApiResponse<bool>.BadRequestResponse("SEO bilgisi güncellenemedi."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<bool>.ErrorResponse($"Sunucu hatası: {ex.Message}"));
        }
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse<bool>>> Delete(Guid id)
    {
        try
        {
            var result = await _productCategorySeoService.DeleteAsync(id);

            if (result)
            {
                return Ok(ApiResponse<bool>.SuccessResponse(true, "SEO bilgisi başarıyla silindi."));
            }

            return BadRequest(ApiResponse<bool>.BadRequestResponse("SEO bilgisi silinemedi."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<bool>.ErrorResponse($"Sunucu hatası: {ex.Message}"));
        }
    }

    [HttpDelete("category/{categoryId}")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteByCategoryId(Guid categoryId)
    {
        try
        {
            var result = await _productCategorySeoService.DeleteByCategoryIdAsync(categoryId);

            if (result)
            {
                return Ok(ApiResponse<bool>.SuccessResponse(true, "Kategori SEO bilgisi başarıyla silindi."));
            }

            return BadRequest(ApiResponse<bool>.BadRequestResponse("Kategori SEO bilgisi silinemedi."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<bool>.ErrorResponse($"Sunucu hatası: {ex.Message}"));
        }
    }

    [HttpPost("category/{categoryId}/generate-auto")]
    public async Task<ActionResult<ApiResponse<bool>>> GenerateAutoSeo(Guid categoryId, [FromBody] GenerateAutoSeoRequest request)
    {
        try
        {
            var result = await _productCategorySeoService.GenerateAutoSeoAsync(categoryId, request.CategoryName, request.Description);

            if (result)
            {
                return Ok(ApiResponse<bool>.SuccessResponse(true, "Otomatik SEO bilgisi başarıyla oluşturuldu."));
            }

            return BadRequest(ApiResponse<bool>.BadRequestResponse("Otomatik SEO bilgisi oluşturulamadı."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<bool>.ErrorResponse($"Sunucu hatası: {ex.Message}"));
        }
    }
}

public class GenerateAutoSeoRequest
{
    public string CategoryName { get; set; } = null!;
    public string? Description { get; set; }
}
