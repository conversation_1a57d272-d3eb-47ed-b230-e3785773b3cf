using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class CouponController : ControllerBase
{
    private readonly ICouponService _couponService;
    private readonly ICustomerAuthService _customerAuthService;

    public CouponController(ICouponService couponService, ICustomerAuthService customerAuthService)
    {
        _couponService = couponService;
        _customerAuthService = customerAuthService;
    }

    /// <summary>
    /// Get available coupons for authenticated customer
    /// </summary>
    [HttpGet("available")]
    public async Task<ActionResult<ApiResponse<List<CouponListDto>>>> GetAvailableCoupons()
    {
        try
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return Unauthorized(ApiResponse<List<CouponListDto>>.ErrorResponse("Token bulunamadı.", 401));
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            var customer = await _customerAuthService.ValidateTokenAsync(token);

            if (customer == null)
            {
                return Unauthorized(ApiResponse<List<CouponListDto>>.ErrorResponse("Geçersiz token.", 401));
            }

            var coupons = await _couponService.GetCustomerActiveCouponsAsync(customer.Id);
            return Ok(ApiResponse<List<CouponListDto>>.SuccessResponse(coupons, "Kullanılabilir kuponlar başarıyla getirildi."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CouponListDto>>.ErrorResponse("Kuponlar getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Validate coupon for order
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<ApiResponse<CouponValidationDto>>> ValidateCouponForOrder([FromBody] ValidateCouponForOrderDto dto)
    {
        try
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return Unauthorized(ApiResponse<CouponValidationDto>.ErrorResponse("Token bulunamadı.", 401));
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            var customer = await _customerAuthService.ValidateTokenAsync(token);

            if (customer == null)
            {
                return Unauthorized(ApiResponse<CouponValidationDto>.ErrorResponse("Geçersiz token.", 401));
            }

            // Override customer ID from token for security
            dto.CustomerId = customer.Id;

            var validation = await _couponService.ValidateCouponForOrderAsync(dto.CouponCode, dto.CustomerId, dto.OrderAmount);
            return Ok(ApiResponse<CouponValidationDto>.SuccessResponse(validation, "Kupon doğrulaması tamamlandı."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<CouponValidationDto>.ErrorResponse(ex.Message));
        }
    }
}
