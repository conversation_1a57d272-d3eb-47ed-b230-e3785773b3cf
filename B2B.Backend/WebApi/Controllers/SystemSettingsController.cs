using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SystemSettingsController : ControllerBase
{
    private readonly ISystemSettingsService _systemSettingsService;

    public SystemSettingsController(ISystemSettingsService systemSettingsService)
    {
        _systemSettingsService = systemSettingsService;
    }

    /// <summary>
    /// Anahtar'a göre sistem ayarı getir (B2C için public endpoint)
    /// </summary>
    [HttpGet("by-key/{key}")]
    public async Task<ActionResult<ApiResponse<SystemSettingsDto>>> GetByKey(string key)
    {
        try
        {
            var setting = await _systemSettingsService.GetByKeyAsync(key);
            if (setting == null)
                return NotFound(ApiResponse<SystemSettingsDto>.NotFoundResponse("Sistem ayarı bulunamadı"));

            return Ok(ApiResponse<SystemSettingsDto>.SuccessResponse(setting, "Sistem ayarı başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<SystemSettingsDto>.BadRequestResponse(ex.Message));
        }
    }

    /// <summary>
    /// Kategoriye göre sistem ayarlarını getir (B2C için public endpoint)
    /// </summary>
    [HttpGet("by-category/{category}")]
    public async Task<ActionResult<ApiResponse<List<SystemSettingsDto>>>> GetByCategory(string category)
    {
        try
        {
            var settings = await _systemSettingsService.GetByCategoryAsync(category);
            return Ok(ApiResponse<List<SystemSettingsDto>>.SuccessResponse(settings, "Sistem ayarları başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<List<SystemSettingsDto>>.BadRequestResponse(ex.Message));
        }
    }

    /// <summary>
    /// Tüm sistem ayarlarını getir (B2C için public endpoint - sadece aktif olanlar)
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<List<SystemSettingsDto>>>> GetAll()
    {
        try
        {
            var settings = await _systemSettingsService.GetAllAsync();
            // B2C için sadece aktif ayarları döndür
            var activeSettings = settings.Where(s => s.IsActive && !s.IsDeleted).ToList();
            return Ok(ApiResponse<List<SystemSettingsDto>>.SuccessResponse(activeSettings, "Sistem ayarları başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<List<SystemSettingsDto>>.BadRequestResponse(ex.Message));
        }
    }

    /// <summary>
    /// Ayar değerini string olarak getir (B2C için basit endpoint)
    /// </summary>
    [HttpGet("value/{key}")]
    public async Task<ActionResult<ApiResponse<string>>> GetValue(string key)
    {
        try
        {
            var value = await _systemSettingsService.GetValueAsync(key);
            if (value == null)
                return NotFound(ApiResponse<string>.NotFoundResponse("Sistem ayarı bulunamadı"));

            return Ok(ApiResponse<string>.SuccessResponse(value, "Ayar değeri başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<string>.BadRequestResponse(ex.Message));
        }
    }
}
