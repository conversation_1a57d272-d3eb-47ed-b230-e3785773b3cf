# Build aşaması
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy csproj files and restore dependencies
COPY ["WebApi/WebApi.csproj", "WebApi/"]
COPY ["Core/Core.csproj", "Core/"]
COPY ["Infrastructure/Infrastructure.csproj", "Infrastructure/"]
COPY ["Application.Contracts/Application.Contracts.csproj", "Application.Contracts/"]
COPY ["Modules/Payments.Abstractions/Payments.Abstractions.csproj", "Modules/Payments.Abstractions/"]
COPY ["Modules/Payments.Implementation/Payments.Implementation.csproj", "Modules/Payments.Implementation/"]
COPY ["Modules/Payments.Iyzico/Payments.Iyzico.csproj", "Modules/Payments.Iyzico/"]

RUN dotnet restore "WebApi/WebApi.csproj"

# Copy everything else and build
COPY . .
WORKDIR "/src/WebApi"
RUN dotnet build "WebApi.csproj" -c Release -o /app/build

# Publish aşaması
FROM build AS publish
RUN dotnet publish "WebApi.csproj" -c Release -o /app/publish

# Runtime aşaması
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
WORKDIR /app
COPY --from=publish /app/publish .

EXPOSE 5000
ENTRYPOINT ["dotnet", "WebApi.dll"]
