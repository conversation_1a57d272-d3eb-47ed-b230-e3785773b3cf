using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Core.Entities;

[Table("SystemSettings")]
public class SystemSettings : BaseEntity
{
    /// <summary>
    /// Ayar anahtarı (örn: "PointValidationDays")
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Key { get; set; } = null!;

    /// <summary>
    /// Ayar değeri
    /// </summary>
    [Required]
    [MaxLength(30000)]
    public string Value { get; set; } = null!;

    /// <summary>
    /// Ayar açıklaması
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Ayar kategorisi (örn: "Points", "General", "Email")
    /// </summary>
    [MaxLength(50)]
    public string? Category { get; set; }

    /// <summary>
    /// Ayar veri tipi (string, int, bool, decimal)
    /// </summary>
    [MaxLength(20)]
    public string DataType { get; set; } = "string";

    /// <summary>
    /// Sistem ayarı mı? (silinemeyen)
    /// </summary>
    public bool IsSystem { get; set; } = false;

    public static void Configure(EntityTypeBuilder<SystemSettings> builder)
    {
        // Key unique olmalı
        builder.HasIndex(s => s.Key).IsUnique();
        
        // Category index
        builder.HasIndex(s => s.Category);
        
        // IsSystem index
        builder.HasIndex(s => s.IsSystem);
    }
}

[Table("SystemSettingsHistory")]
public class SystemSettingsHistory : HistoryBaseEntity
{
    public string Key { get; set; } = null!;
    public string Value { get; set; } = null!;
    public string? Description { get; set; }
    public string? Category { get; set; }
    public string DataType { get; set; } = "string";
    public bool IsSystem { get; set; } = false;
}
