using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Core.Entities;

[Table("ProductCategorySeos")]
public partial class ProductCategorySeo : BaseEntity
{
    public Guid ProductCategoryId { get; set; }
    
    [MaxLength(200)]
    public string? MetaTitle { get; set; }
    
    [MaxLength(500)]
    public string? MetaDescription { get; set; }
    
    [MaxLength(300)]
    public string? MetaKeywords { get; set; }
    
    [MaxLength(200)]
    public string? OgTitle { get; set; }
    
    [MaxLength(500)]
    public string? OgDescription { get; set; }
    
    [MaxLength(500)]
    public string? OgImage { get; set; }
    
    [MaxLength(2000)]
    public string? StructuredData { get; set; }
    
    [MaxLength(500)]
    public string? CanonicalUrl { get; set; }
    
    public bool NoIndex { get; set; } = false;
    public bool NoFollow { get; set; } = false;

    // Navigation property
    [ForeignKey(nameof(ProductCategoryId))]
    public ProductCategory ProductCategory { get; set; } = null!;

    public static void Configure(EntityTypeBuilder<ProductCategorySeo> builder)
    {
        builder.HasIndex(s => s.ProductCategoryId).IsUnique();
        
        builder.HasOne(s => s.ProductCategory)
            .WithOne()
            .HasForeignKey<ProductCategorySeo>(s => s.ProductCategoryId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

[Table("ProductCategorySeosHistory")]
public class ProductCategorySeoHistory : HistoryBaseEntity
{
    // Entity properties
    public Guid ProductCategoryId { get; set; }
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public string? MetaKeywords { get; set; }
    public string? OgTitle { get; set; }
    public string? OgDescription { get; set; }
    public string? OgImage { get; set; }
    public string? StructuredData { get; set; }
    public string? CanonicalUrl { get; set; }
    public bool NoIndex { get; set; }
    public bool NoFollow { get; set; }
}
