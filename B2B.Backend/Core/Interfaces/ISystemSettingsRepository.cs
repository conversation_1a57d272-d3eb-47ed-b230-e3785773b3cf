using Core.Entities;
using Core.Enums;

namespace Core.Interfaces;

public interface ISystemSettingsRepository : IGenericRepository<SystemSettings>
{
    /// <summary>
    /// Ayar anahtarına göre ayar getir
    /// </summary>
    Task<SystemSettings?> GetByKeyAsync(string key);

    /// <summary>
    /// Kategoriye göre ayarları getir
    /// </summary>
    Task<List<SystemSettings>> GetByCategoryAsync(string category);

    /// <summary>
    /// Sistem ayarlarını getir
    /// </summary>
    Task<List<SystemSettings>> GetSystemSettingsAsync();

    /// <summary>
    /// Ayar değerini string olarak getir
    /// </summary>
    Task<string?> GetValueAsync(string key);

    /// <summary>
    /// Ayar değerini int olarak getir
    /// </summary>
    Task<int?> GetIntValueAsync(string key);

    /// <summary>
    /// Ayar değerini decimal olarak getir
    /// </summary>
    Task<decimal?> GetDecimalValueAsync(string key);

    /// <summary>
    /// Ayar değerini bool olarak getir
    /// </summary>
    Task<bool?> GetBoolValueAsync(string key);

    /// <summary>
    /// Ayar değerini güncelle
    /// </summary>
    Task<bool> UpdateValueAsync(string key, string value);

    /// <summary>
    /// History'ye ekle
    /// </summary>
    Task AddToHistoryAsync(SystemSettings systemSettings, ChangeType changeType, Guid employeeId);
}
