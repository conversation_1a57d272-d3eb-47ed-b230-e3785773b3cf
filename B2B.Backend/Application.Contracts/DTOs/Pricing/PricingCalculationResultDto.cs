namespace Application.Contracts.DTOs.Pricing;

/// <summary>
/// Tek ürün fiyatlandırma hesaplama sonucu
/// </summary>
public class PricingCalculationResultDto
{
    /// <summary>
    /// Orijinal fiyat (Product.Price)
    /// </summary>
    public decimal OriginalPrice { get; set; }

    /// <summary>
    /// İndirimli fiyat (Product.DiscountedPrice)
    /// </summary>
    public decimal? DiscountedPrice { get; set; }

    /// <summary>
    /// Miktar
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// İndirim tutarı (ProductPrice - DiscountPrice) * Quantity
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// Vergi tutarı (EffectivePrice - EffectivePrice / 1.2) * Quantity
    /// </summary>
    public decimal TaxAmount { get; set; }

    /// <summary>
    /// Net tutar (vergi hariç) (EffectivePrice / 1.2) * Quantity
    /// </summary>
    public decimal NetAmount { get; set; }

    /// <summary>
    /// Toplam tutar (vergi dahil) EffectivePrice * Quantity
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// İndirim var mı?
    /// </summary>
    public bool HasDiscount { get; set; }

    /// <summary>
    /// Kullanılan efektif fiyat (DiscountedPrice ?? OriginalPrice)
    /// </summary>
    public decimal EffectivePrice => DiscountedPrice ?? OriginalPrice;
    public decimal CouponDiscountAmount { get; set; }
    public decimal PointDiscountAmount { get; set; }
}
