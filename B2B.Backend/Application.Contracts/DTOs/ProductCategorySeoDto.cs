using System.ComponentModel.DataAnnotations;

namespace Application.Contracts.DTOs;

public class ProductCategorySeoDto
{
    public Guid Id { get; set; }
    public Guid ProductCategoryId { get; set; }
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public string? MetaKeywords { get; set; }
    public string? OgTitle { get; set; }
    public string? OgDescription { get; set; }
    public string? OgImage { get; set; }
    public string? StructuredData { get; set; }
    public string? CanonicalUrl { get; set; }
    public bool NoIndex { get; set; } = false;
    public bool NoFollow { get; set; } = false;
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ProductCategorySeoCreateDto
{
    [Required]
    public Guid ProductCategoryId { get; set; }
    
    [StringLength(200)]
    public string? MetaTitle { get; set; }
    
    [StringLength(500)]
    public string? MetaDescription { get; set; }
    
    [StringLength(300)]
    public string? MetaKeywords { get; set; }
    
    [StringLength(200)]
    public string? OgTitle { get; set; }
    
    [StringLength(500)]
    public string? OgDescription { get; set; }
    
    [StringLength(500)]
    public string? OgImage { get; set; }
    
    [StringLength(2000)]
    public string? StructuredData { get; set; }
    
    [StringLength(500)]
    public string? CanonicalUrl { get; set; }
    
    public bool NoIndex { get; set; } = false;
    public bool NoFollow { get; set; } = false;
}

public class ProductCategorySeoUpdateDto
{
    [Required]
    public Guid Id { get; set; }
    
    [Required]
    public Guid ProductCategoryId { get; set; }
    
    [StringLength(200)]
    public string? MetaTitle { get; set; }
    
    [StringLength(500)]
    public string? MetaDescription { get; set; }
    
    [StringLength(300)]
    public string? MetaKeywords { get; set; }
    
    [StringLength(200)]
    public string? OgTitle { get; set; }
    
    [StringLength(500)]
    public string? OgDescription { get; set; }
    
    [StringLength(500)]
    public string? OgImage { get; set; }
    
    [StringLength(2000)]
    public string? StructuredData { get; set; }
    
    [StringLength(500)]
    public string? CanonicalUrl { get; set; }
    
    public bool NoIndex { get; set; } = false;
    public bool NoFollow { get; set; } = false;
}
