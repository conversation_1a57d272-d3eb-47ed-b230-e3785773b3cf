using System.ComponentModel.DataAnnotations;

namespace Application.Contracts.DTOs;

public class SystemSettingsDto
{
    public Guid Id { get; set; }
    public string Key { get; set; } = null!;
    public string Value { get; set; } = null!;
    public string? Description { get; set; }
    public string? Category { get; set; }
    public string DataType { get; set; } = "string";
    public bool IsSystem { get; set; }
    public bool IsActive { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class SystemSettingsCreateDto
{
    [Required]
    [StringLength(100, MinimumLength = 1)]
    public string Key { get; set; } = null!;

    [Required]
    [StringLength(500, MinimumLength = 1)]
    public string Value { get; set; } = null!;

    [StringLength(1000)]
    public string? Description { get; set; }

    [StringLength(50)]
    public string? Category { get; set; }

    [StringLength(20)]
    public string DataType { get; set; } = "string";

    public bool IsSystem { get; set; } = false;
}

public class SystemSettingsUpdateDto
{
    public Guid Id { get; set; }

    [Required]
    [StringLength(500, MinimumLength = 1)]
    public string Value { get; set; } = null!;

    [StringLength(1000)]
    public string? Description { get; set; }

    [StringLength(50)]
    public string? Category { get; set; }

    [StringLength(20)]
    public string DataType { get; set; } = "string";
}

public class SystemSettingsListDto
{
    public Guid Id { get; set; }
    public string Key { get; set; } = null!;
    public string Value { get; set; } = null!;
    public string? Description { get; set; }
    public string? Category { get; set; }
    public string DataType { get; set; } = "string";
    public bool IsSystem { get; set; }
    public DateTime UpdatedAt { get; set; }
}
