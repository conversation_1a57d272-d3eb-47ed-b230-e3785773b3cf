using Application.Contracts.DTOs;
using Application.Contracts.DTOs.Pricing;
using Core.Enums;

namespace Application.Contracts.Interfaces;

/// <summary>
/// Merkezi fiyatlandırma hesaplama servisi interface'i
/// Product.Price ve Product.DiscountedPrice kullanarak indirim ve vergi hesaplamaları yapar
/// </summary>
public interface IPricingCalculationService
{
    /// <summary>
    /// Tek ürün için fiyatlandırma hesaplaması yapar
    /// </summary>
    /// <param name="price"><PERSON>rün fiyatı (Product.Price)</param>
    /// <param name="discountedPrice"><PERSON>ndi<PERSON><PERSON> fiyat (Product.DiscountedPrice)</param>
    /// <param name="quantity">Miktar</param>
    /// <returns>Hesaplama sonucu</returns>
    PricingCalculationResultDto CalculateItemPricing(decimal price, decimal? discountedPrice, int quantity = 1);

    /// <summary>
    /// Sepet için toplam fiyatlandırma hesaplaması yapar
    /// </summary>
    /// <param name="items">Sepet ürünleri hesaplama sonuçları</param>
    /// <returns>Sepet özeti</returns>
    PricingCalculationSummaryDto CalculateCartPricing(List<PricingCalculationResultDto> items);

    /// <summary>
    /// Sipariş için toplam fiyatlandırma hesaplaması yapar (kargo ve kampanya dahil)
    /// </summary>
    /// <param name="items">Sipariş ürünleri hesaplama sonuçları</param>
    /// <param name="shippingAmount">Kargo tutarı</param>
    /// <param name="campaignDiscountAmount">Kampanya indirim tutarı</param>
    /// <returns>Sipariş özeti</returns>
    PricingCalculationSummaryDto CalculateOrderPricing(List<PricingCalculationResultDto> items,
        decimal shippingAmount = 0, decimal campaignDiscountAmount = 0);

    /// <summary>
    /// Vergi dahil tutardan vergi miktarını hesaplar
    /// </summary>
    /// <param name="grossAmount">Vergi dahil tutar</param>
    /// <returns>Vergi tutarı</returns>
    decimal CalculateTaxFromGrossAmount(decimal grossAmount);

    /// <summary>
    /// Vergi dahil tutardan net tutarı hesaplar
    /// </summary>
    /// <param name="grossAmount">Vergi dahil tutar</param>
    /// <returns>Net tutar (vergi hariç)</returns>
    decimal CalculateNetFromGrossAmount(decimal grossAmount);

    /// <summary>
    /// İndirim tutarını hesaplar
    /// </summary>
    /// <param name="originalPrice">Orijinal fiyat</param>
    /// <param name="discountedPrice">İndirimli fiyat</param>
    /// <param name="quantity">Miktar</param>
    /// <returns>İndirim tutarı</returns>
    decimal CalculateDiscountAmount(decimal originalPrice, decimal discountedPrice, int quantity = 1);
    decimal CalculateCouponDiscountAmount(decimal orderAmount,CouponDto? coupon);
    decimal CalculatePointDiscountAmount(decimal orderAmount, decimal pointDiscountAmount);
}
