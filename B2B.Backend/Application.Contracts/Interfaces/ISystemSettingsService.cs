using Application.Contracts.DTOs;

namespace Application.Contracts.Interfaces;

public interface ISystemSettingsService
{
    /// <summary>
    /// Tüm ayarları getir
    /// </summary>
    Task<List<SystemSettingsDto>> GetAllAsync();

    /// <summary>
    /// ID'ye göre ayar getir
    /// </summary>
    Task<SystemSettingsDto?> GetByIdAsync(Guid id);

    /// <summary>
    /// Anahtar'a göre ayar getir
    /// </summary>
    Task<SystemSettingsDto?> GetByKeyAsync(string key);

    /// <summary>
    /// Kategoriye göre ayarları getir
    /// </summary>
    Task<List<SystemSettingsDto>> GetByCategoryAsync(string category);

    /// <summary>
    /// Sistem ayarlarını getir
    /// </summary>
    Task<List<SystemSettingsDto>> GetSystemSettingsAsync();

    /// <summary>
    /// Yeni ayar oluştur
    /// </summary>
    Task<Guid> CreateAsync(SystemSettingsCreateDto dto);

    /// <summary>
    /// Ayar güncelle
    /// </summary>
    Task UpdateAsync(SystemSettingsUpdateDto dto);

    /// <summary>
    /// Ayar sil
    /// </summary>
    Task DeleteAsync(Guid id);

    /// <summary>
    /// Ayar değerini string olarak getir
    /// </summary>
    Task<string?> GetValueAsync(string key);

    /// <summary>
    /// Ayar değerini int olarak getir
    /// </summary>
    Task<int?> GetIntValueAsync(string key);

    /// <summary>
    /// Ayar değerini decimal olarak getir
    /// </summary>
    Task<decimal?> GetDecimalValueAsync(string key);

    /// <summary>
    /// Ayar değerini bool olarak getir
    /// </summary>
    Task<bool?> GetBoolValueAsync(string key);

    /// <summary>
    /// Ayar değerini güncelle
    /// </summary>
    Task<bool> UpdateValueAsync(string key, string value);

    /// <summary>
    /// Puan geçerlilik gün sayısını getir
    /// </summary>
    Task<int> GetPointValidationDaysAsync();
}
