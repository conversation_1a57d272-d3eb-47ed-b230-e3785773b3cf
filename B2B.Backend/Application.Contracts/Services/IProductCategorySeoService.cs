using Application.Contracts.DTOs;

namespace Application.Contracts.Services;

public interface IProductCategorySeoService
{
    Task<ProductCategorySeoDto?> GetByIdAsync(Guid id);
    Task<ProductCategorySeoDto?> GetByCategoryIdAsync(Guid categoryId);
    Task<IEnumerable<ProductCategorySeoDto>> GetAllAsync();
    Task<bool> CreateOrUpdateAsync(Guid categoryId, ProductCategorySeoCreateDto dto);
    Task<bool> UpdateAsync(ProductCategorySeoUpdateDto dto);
    Task<bool> DeleteAsync(Guid id);
    Task<bool> DeleteByCategoryIdAsync(Guid categoryId);
    Task<bool> GenerateAutoSeoAsync(Guid categoryId, string categoryName, string? description = null);
}
