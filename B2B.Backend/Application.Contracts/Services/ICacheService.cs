namespace Application.Contracts.Services;

/// <summary>
/// Cache service interface for server-side caching
/// </summary>
public interface ICacheService
{
    /// <summary>
    /// Get cached value by key
    /// </summary>
    /// <typeparam name="T">Type of cached value</typeparam>
    /// <param name="key">Cache key</param>
    /// <returns>Cached value or default(T) if not found</returns>
    Task<T?> GetAsync<T>(string key) where T : class;

    /// <summary>
    /// Set cache value with expiration
    /// </summary>
    /// <typeparam name="T">Type of value to cache</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="value">Value to cache</param>
    /// <param name="expiration">Cache expiration time</param>
    /// <returns>True if successful</returns>
    Task<bool> SetAsync<T>(string key, T value, TimeSpan expiration) where T : class;

    /// <summary>
    /// Remove cached value by key
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <returns>True if removed</returns>
    Task<bool> RemoveAsync(string key);

    /// <summary>
    /// Check if key exists in cache
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <returns>True if exists</returns>
    Task<bool> ExistsAsync(string key);

    /// <summary>
    /// Clear all cache entries
    /// </summary>
    /// <returns>True if successful</returns>
    Task<bool> ClearAsync();

    /// <summary>
    /// Get or set cached value with factory function
    /// </summary>
    /// <typeparam name="T">Type of cached value</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="factory">Factory function to create value if not cached</param>
    /// <param name="expiration">Cache expiration time</param>
    /// <returns>Cached or newly created value</returns>
    Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, TimeSpan expiration) where T : class;
}
