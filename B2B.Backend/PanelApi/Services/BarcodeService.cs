using ZXing;
using ZXing.Common;
using ZXing.SkiaSharp;
using SkiaSharp;

namespace PanelApi.Services;

/// <summary>
/// Barkod üretimi için servis
/// </summary>
public interface IBarcodeService
{
    /// <summary>
    /// Verilen metni barkoda dönüştürür ve Base64 string olarak döndürür
    /// </summary>
    /// <param name="text">Barkoda dönüştürülecek metin</param>
    /// <param name="width"><PERSON>kod geni<PERSON>ği (varsayılan: 300)</param>
    /// <param name="height"><PERSON><PERSON>d yüksekliği (varsayılan: 100)</param>
    /// <returns>Base64 formatında barkod resmi</returns>
    string GenerateBarcode(string text, int width = 900, int height = 200);
}

/// <summary>
/// Barkod üretimi servis implementasyonu
/// </summary>
public class BarcodeService : IBarcodeService
{
    public string GenerateBarcode(string text, int width = 900, int height = 200)
    {
        try
        {
            // Daha basit ve güvenilir yaklaşım: SKBitmap ile direkt çizim
            var info = new SKImageInfo(width, height, SKColorType.Rgba8888, SKAlphaType.Premul);
            using var surface = SKSurface.Create(info);
            using var canvas = surface.Canvas;

            // Beyaz arka plan
            canvas.Clear(SKColors.White);

            // ZXing writer'ı kullanarak boolean matrix al
            var writer = new BarcodeWriter
            {
                Format = BarcodeFormat.CODE_128,
                Options = new EncodingOptions
                {
                    Width = width,
                    Height = height - 40, // Alt kısımda metin için yer bırak
                    Margin = 10,
                    PureBarcode = true // Sadece barkod, metin ayrı çizeceğiz
                }
            };

            var matrix = writer.Encode(text);

            // Barkod çizimi
            var barWidth = (float)(width - 20) / matrix.Width; // 20 pixel margin
            var barHeight = (float)(height - 60) / matrix.Height; // 60 pixel alt boşluk

            using var blackPaint = new SKPaint { Color = SKColors.Black };

            for (int y = 0; y < matrix.Height; y++)
            {
                for (int x = 0; x < matrix.Width; x++)
                {
                    if (matrix[x, y])
                    {
                        var rect = new SKRect(
                            10 + x * barWidth,
                            10 + y * barHeight,
                            10 + (x + 1) * barWidth,
                            10 + (y + 1) * barHeight
                        );
                        canvas.DrawRect(rect, blackPaint);
                    }
                }
            }

            // Metin çizimi
            using var textPaint = new SKPaint
            {
                Color = SKColors.Black,
                TextSize = 16,
                IsAntialias = true,
                Typeface = SKTypeface.FromFamilyName("Arial", SKFontStyle.Normal)
            };

            var textBounds = new SKRect();
            textPaint.MeasureText(text, ref textBounds);
            var textX = (width - textBounds.Width) / 2;
            var textY = height - 15;

            canvas.DrawText(text, textX, textY, textPaint);

            // PNG olarak encode et
            using var image = surface.Snapshot();
            using var data = image.Encode(SKEncodedImageFormat.Png, 100);

            return Convert.ToBase64String(data.ToArray());
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Barkod oluşturulurken hata oluştu: {ex.Message}", ex);
        }
    }
}
