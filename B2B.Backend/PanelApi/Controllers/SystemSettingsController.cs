using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using PanelApi.Attributes;

namespace PanelApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[EnableCors("AllowFrontend")]
[Authorize]
public class SystemSettingsController : ControllerBase
{
    private readonly ISystemSettingsService _systemSettingsService;

    public SystemSettingsController(ISystemSettingsService systemSettingsService)
    {
        _systemSettingsService = systemSettingsService;
    }

    /// <summary>
    /// Tüm sistem ayarlarını getir
    /// </summary>
    [HttpGet]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult<List<SystemSettingsDto>>> GetAll()
    {
        try
        {
            var settings = await _systemSettingsService.GetAllAsync();
            return Ok(settings);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// ID'ye göre sistem ayarı getir
    /// </summary>
    [HttpGet("{id}")]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult<SystemSettingsDto>> GetById(Guid id)
    {
        try
        {
            var setting = await _systemSettingsService.GetByIdAsync(id);
            if (setting == null)
                return NotFound(new { message = "Sistem ayarı bulunamadı." });

            return Ok(setting);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Anahtar'a göre sistem ayarı getir
    /// </summary>
    [HttpGet("by-key/{key}")]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult<SystemSettingsDto>> GetByKey(string key)
    {
        try
        {
            var setting = await _systemSettingsService.GetByKeyAsync(key);
            if (setting == null)
                return NotFound(new { message = "Sistem ayarı bulunamadı." });

            return Ok(setting);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Kategoriye göre sistem ayarlarını getir
    /// </summary>
    [HttpGet("by-category/{category}")]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult<List<SystemSettingsDto>>> GetByCategory(string category)
    {
        try
        {
            var settings = await _systemSettingsService.GetByCategoryAsync(category);
            return Ok(settings);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Sistem ayarlarını getir (IsSystem = true)
    /// </summary>
    [HttpGet("system")]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult<List<SystemSettingsDto>>> GetSystemSettings()
    {
        try
        {
            var settings = await _systemSettingsService.GetSystemSettingsAsync();
            return Ok(settings);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Yeni sistem ayarı oluştur
    /// </summary>
    [HttpPost]
    [RequirePermission("settings", "create")]
    public async Task<ActionResult> Create([FromBody] SystemSettingsCreateDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var id = await _systemSettingsService.CreateAsync(dto);
            return Ok(new { message = "Sistem ayarı başarıyla oluşturuldu.", id = id });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Sistem ayarını güncelle
    /// </summary>
    [HttpPut("{id}")]
    [RequirePermission("settings", "update")]
    public async Task<ActionResult> Update(Guid id, [FromBody] SystemSettingsUpdateDto dto)
    {
        try
        {
            if (id != dto.Id)
                return BadRequest(new { message = "ID uyuşmazlığı" });

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            await _systemSettingsService.UpdateAsync(dto);
            return Ok(new { message = "Sistem ayarı başarıyla güncellendi." });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Sistem ayarını sil
    /// </summary>
    [HttpDelete("{id}")]
    [RequirePermission("settings", "delete")]
    public async Task<ActionResult> Delete(Guid id)
    {
        try
        {
            await _systemSettingsService.DeleteAsync(id);
            return Ok(new { message = "Sistem ayarı başarıyla silindi." });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Ayar değerini güncelle (sadece value)
    /// </summary>
    [HttpPatch("{key}/value")]
    [RequirePermission("settings", "update")]
    public async Task<ActionResult> UpdateValue(string key, [FromBody] string value)
    {
        try
        {
            var result = await _systemSettingsService.UpdateValueAsync(key, value);
            if (!result)
                return NotFound(new { message = "Sistem ayarı bulunamadı." });

            return Ok(new { message = "Ayar değeri başarıyla güncellendi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }
}
