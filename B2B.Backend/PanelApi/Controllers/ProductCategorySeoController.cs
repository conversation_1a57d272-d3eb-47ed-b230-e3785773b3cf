using Application.Contracts.DTOs;
using Application.Contracts.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace PanelApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ProductCategorySeoController : ControllerBase
{
    private readonly IProductCategorySeoService _productCategorySeoService;
    private readonly ILogger<ProductCategorySeoController> _logger;

    public ProductCategorySeoController(
        IProductCategorySeoService productCategorySeoService,
        ILogger<ProductCategorySeoController> logger)
    {
        _productCategorySeoService = productCategorySeoService;
        _logger = logger;
    }

    /// <summary>
    /// Get all category SEO records
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<IEnumerable<ProductCategorySeoDto>>>> GetAll()
    {
        try
        {
            var seos = await _productCategorySeoService.GetAllAsync();
            return Ok(ApiResponse<IEnumerable<ProductCategorySeoDto>>.SuccessResponse(seos, "SEO kayıtları başarıyla getirildi."));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all category SEO records");
            return StatusCode(500, ApiResponse<IEnumerable<ProductCategorySeoDto>>.ErrorResponse("Sunucu hatası oluştu."));
        }
    }

    /// <summary>
    /// Get category SEO by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<ProductCategorySeoDto>>> GetById(Guid id)
    {
        try
        {
            var seo = await _productCategorySeoService.GetByIdAsync(id);
            if (seo == null)
            {
                return NotFound(ApiResponse<ProductCategorySeoDto>.NotFoundResponse("SEO kaydı bulunamadı."));
            }

            return Ok(ApiResponse<ProductCategorySeoDto>.SuccessResponse(seo, "SEO kaydı başarıyla getirildi."));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting category SEO by id: {Id}", id);
            return StatusCode(500, ApiResponse<ProductCategorySeoDto>.ErrorResponse("Sunucu hatası oluştu."));
        }
    }

    /// <summary>
    /// Get category SEO by category ID
    /// </summary>
    [HttpGet("category/{categoryId}")]
    public async Task<ActionResult<ApiResponse<ProductCategorySeoDto>>> GetByCategoryId(Guid categoryId)
    {
        try
        {
            var seo = await _productCategorySeoService.GetByCategoryIdAsync(categoryId);
            if (seo == null)
            {
                return NotFound(ApiResponse<ProductCategorySeoDto>.NotFoundResponse("Kategori için SEO kaydı bulunamadı."));
            }

            return Ok(ApiResponse<ProductCategorySeoDto>.SuccessResponse(seo, "Kategori SEO kaydı başarıyla getirildi."));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting category SEO by category id: {CategoryId}", categoryId);
            return StatusCode(500, ApiResponse<ProductCategorySeoDto>.ErrorResponse("Sunucu hatası oluştu."));
        }
    }

    /// <summary>
    /// Create or update category SEO
    /// </summary>
    [HttpPost("category/{categoryId}")]
    public async Task<ActionResult<ApiResponse<bool>>> CreateOrUpdate(Guid categoryId, [FromBody] ProductCategorySeoCreateDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                return BadRequest(ApiResponse<bool>.BadRequestResponse($"Geçersiz veri: {string.Join(", ", errors)}"));
            }

            dto.ProductCategoryId = categoryId;
            var result = await _productCategorySeoService.CreateOrUpdateAsync(categoryId, dto);

            if (result)
            {
                return Ok(ApiResponse<bool>.SuccessResponse(true, "SEO kaydı başarıyla kaydedildi."));
            }

            return BadRequest(ApiResponse<bool>.BadRequestResponse("SEO kaydı kaydedilemedi."));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating/updating category SEO for category: {CategoryId}", categoryId);
            return StatusCode(500, ApiResponse<bool>.ErrorResponse("Sunucu hatası oluştu."));
        }
    }

    /// <summary>
    /// Update category SEO
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<bool>>> Update(Guid id, [FromBody] ProductCategorySeoUpdateDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                return BadRequest(ApiResponse<bool>.BadRequestResponse($"Geçersiz veri: {string.Join(", ", errors)}"));
            }

            if (id != dto.Id)
            {
                return BadRequest(ApiResponse<bool>.BadRequestResponse("ID uyuşmazlığı."));
            }

            var result = await _productCategorySeoService.UpdateAsync(dto);

            if (result)
            {
                return Ok(ApiResponse<bool>.SuccessResponse(true, "SEO kaydı başarıyla güncellendi."));
            }

            return BadRequest(ApiResponse<bool>.BadRequestResponse("SEO kaydı güncellenemedi."));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating category SEO: {Id}", id);
            return StatusCode(500, ApiResponse<bool>.ErrorResponse("Sunucu hatası oluştu."));
        }
    }

    /// <summary>
    /// Delete category SEO by ID
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse<bool>>> Delete(Guid id)
    {
        try
        {
            var result = await _productCategorySeoService.DeleteAsync(id);

            if (result)
            {
                return Ok(ApiResponse<bool>.SuccessResponse(true, "SEO kaydı başarıyla silindi."));
            }

            return BadRequest(ApiResponse<bool>.BadRequestResponse("SEO kaydı silinemedi."));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting category SEO: {Id}", id);
            return StatusCode(500, ApiResponse<bool>.ErrorResponse("Sunucu hatası oluştu."));
        }
    }

    /// <summary>
    /// Delete category SEO by category ID
    /// </summary>
    [HttpDelete("category/{categoryId}")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteByCategoryId(Guid categoryId)
    {
        try
        {
            var result = await _productCategorySeoService.DeleteByCategoryIdAsync(categoryId);

            if (result)
            {
                return Ok(ApiResponse<bool>.SuccessResponse(true, "Kategori SEO kaydı başarıyla silindi."));
            }

            return BadRequest(ApiResponse<bool>.BadRequestResponse("Kategori SEO kaydı silinemedi."));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting category SEO by category id: {CategoryId}", categoryId);
            return StatusCode(500, ApiResponse<bool>.ErrorResponse("Sunucu hatası oluştu."));
        }
    }

    /// <summary>
    /// Generate automatic SEO for category
    /// </summary>
    [HttpPost("category/{categoryId}/generate-auto")]
    public async Task<ActionResult<ApiResponse<bool>>> GenerateAutoSeo(Guid categoryId, [FromBody] GenerateAutoSeoRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.CategoryName))
            {
                return BadRequest(ApiResponse<bool>.BadRequestResponse("Kategori adı gereklidir."));
            }

            var result = await _productCategorySeoService.GenerateAutoSeoAsync(categoryId, request.CategoryName, request.Description);

            if (result)
            {
                return Ok(ApiResponse<bool>.SuccessResponse(true, "Otomatik SEO kaydı başarıyla oluşturuldu."));
            }

            return BadRequest(ApiResponse<bool>.BadRequestResponse("Otomatik SEO kaydı oluşturulamadı."));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating auto SEO for category: {CategoryId}", categoryId);
            return StatusCode(500, ApiResponse<bool>.ErrorResponse("Sunucu hatası oluştu."));
        }
    }
}

/// <summary>
/// Request model for generating automatic SEO
/// </summary>
public class GenerateAutoSeoRequest
{
    public string CategoryName { get; set; } = string.Empty;
    public string? Description { get; set; }
}
