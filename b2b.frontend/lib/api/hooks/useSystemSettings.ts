'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '../client';
import {
  SystemSettingsDto,
  SystemSettingsCreateDto,
  SystemSettingsUpdateDto,
  SystemSettingsFilterParams,
  GroupedSystemSettings
} from '@/types/system-settings';

// Query keys
export const systemSettingsKeys = {
  all: ['system-settings'] as const,
  lists: () => [...systemSettingsKeys.all, 'list'] as const,
  list: (filters: SystemSettingsFilterParams) => [...systemSettingsKeys.lists(), { filters }] as const,
  details: () => [...systemSettingsKeys.all, 'detail'] as const,
  detail: (id: string) => [...systemSettingsKeys.details(), id] as const,
  byKey: (key: string) => [...systemSettingsKeys.all, 'by-key', key] as const,
  byCategory: (category: string) => [...systemSettingsKeys.all, 'by-category', category] as const,
  system: () => [...systemSettingsKeys.all, 'system'] as const,
};

// Fetch all system settings with optional filtering
export const useSystemSettings = (filters: SystemSettingsFilterParams = {}) => {
  return useQuery({
    queryKey: systemSettingsKeys.list(filters),
    queryFn: () => api.get<SystemSettingsDto[]>('/systemsettings', filters),
  });
};

// Fetch a single system setting by ID
export const useSystemSetting = (id: string) => {
  return useQuery({
    queryKey: systemSettingsKeys.detail(id),
    queryFn: () => api.get<SystemSettingsDto>(`/systemsettings/${id}`),
    enabled: !!id, // Only run the query if an ID is provided
  });
};

// Fetch a single system setting by key
export const useSystemSettingByKey = (key: string) => {
  return useQuery({
    queryKey: systemSettingsKeys.byKey(key),
    queryFn: () => api.get<SystemSettingsDto>(`/systemsettings/by-key/${key}`),
    enabled: !!key, // Only run the query if a key is provided
  });
};

// Fetch system settings by category
export const useSystemSettingsByCategory = (category: string) => {
  return useQuery({
    queryKey: systemSettingsKeys.byCategory(category),
    queryFn: () => api.get<SystemSettingsDto[]>(`/systemsettings/by-category/${category}`),
    enabled: !!category, // Only run the query if a category is provided
  });
};

// Fetch system settings (IsSystem = true)
export const useSystemSystemSettings = () => {
  return useQuery({
    queryKey: systemSettingsKeys.system(),
    queryFn: () => api.get<SystemSettingsDto[]>('/systemsettings/system'),
  });
};

// Hook to get grouped system settings by category
export const useGroupedSystemSettings = (filters: SystemSettingsFilterParams = {}) => {
  const { data: settings, ...rest } = useSystemSettings(filters);
  
  const groupedSettings: GroupedSystemSettings = {};
  
  if (settings) {
    settings.forEach(setting => {
      const category = setting.category || 'Uncategorized';
      if (!groupedSettings[category]) {
        groupedSettings[category] = [];
      }
      groupedSettings[category].push(setting);
    });
  }
  
  return {
    data: groupedSettings,
    settings,
    ...rest
  };
};

// Create a new system setting
export const useCreateSystemSetting = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (newSetting: SystemSettingsCreateDto) => 
      api.post<{ message: string; id: string }>('/systemsettings', newSetting),
    onSuccess: () => {
      // Invalidate all system settings queries to refetch the updated list
      queryClient.invalidateQueries({ queryKey: systemSettingsKeys.all });
    },
  });
};

// Update an existing system setting
export const useUpdateSystemSetting = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (setting: SystemSettingsUpdateDto) => 
      api.put<{ message: string }>(`/systemsettings/${setting.id}`, setting),
    onSuccess: (_, variables) => {
      // Invalidate all system settings queries
      queryClient.invalidateQueries({ queryKey: systemSettingsKeys.all });
      // Also invalidate the specific setting detail query
      queryClient.invalidateQueries({ queryKey: systemSettingsKeys.detail(variables.id) });
    },
  });
};

// Delete a system setting
export const useDeleteSystemSetting = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => 
      api.delete<{ message: string }>(`/systemsettings/${id}`),
    onSuccess: () => {
      // Invalidate all system settings queries to refetch the updated list
      queryClient.invalidateQueries({ queryKey: systemSettingsKeys.all });
    },
  });
};

// Update only the value of a system setting
export const useUpdateSystemSettingValue = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ key, value }: { key: string; value: string }) => 
      api.patch<{ message: string }>(`/systemsettings/${key}/value`, value, {
        headers: {
          'Content-Type': 'application/json'
        }
      }),
    onSuccess: () => {
      // Invalidate all system settings queries to refetch the updated list
      queryClient.invalidateQueries({ queryKey: systemSettingsKeys.all });
    },
  });
};
