import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api/client';
import { toast } from 'sonner';

// Types
export interface CouponListDto {
  id: string;
  couponCode: string;
  couponType: number;
  customerId?: string;
  customerName?: string;
  discountTypeText: 'Yüzde' | 'Sabit Tutar';
  discountAmount: number;
  totalUsageLimit?: number;
  usageLimitPerCustomer: number;
  totalUsageCount: number;
  isActive: boolean;
  expirationDate?: string;
  createdAt: string;
  description?: string;
}

export interface CouponCreateDto {
  couponCode: string;
  couponType: number;
  customerId?: string;
  discountTypeText: 'Yüzde' | 'Sabit Tutar';
  discountAmount: number;
  totalUsageLimit?: number;
  usageLimitPerCustomer: number;
  expirationDate?: string;
  description?: string;
  isActive: boolean;
}

export interface CouponUpdateDto {
  id: string;
  couponCode: string;
  couponType: number;
  customerId?: string;
  discountTypeText: 'Yüzde' | 'Sabit Tutar';
  discountAmount: number;
  totalUsageLimit?: number;
  usageLimitPerCustomer: number;
  expirationDate?: string;
  description?: string;
  isActive: boolean;
}

export interface CouponUsageDto {
  couponId: string;
  customerId: string;
  couponCode: string;
  orderId?: string;
  discountApplied: number;
  orderAmount: number;
}

export interface CouponValidationDto {
  isValid: boolean;
  message: string;
  discountAmount?: number;
  discountType?: 'Percentage' | 'FixedAmount';
}

export interface CouponSummaryDto {
  customerId: string;
  totalCoupons: number;
  activeCoupons: number;
  usedCoupons: number;
  expiredCoupons: number;
  recentCoupons: CouponListDto[];
}

// Hooks for general coupon management
export function useCoupons() {
  return useQuery({
    queryKey: ['coupons'],
    queryFn: async () => {
      const response = await api.get('/coupon');
      return response.data as CouponListDto[];
    },
  });
}

export function useGeneralCoupons() {
  return useQuery({
    queryKey: ['coupons', 'general'],
    queryFn: async () => {
      const response = await api.get('/coupon/general');
      return response.data as CouponListDto[];
    },
  });
}

export function useCustomerSpecificCoupons() {
  return useQuery({
    queryKey: ['coupons', 'customer-specific'],
    queryFn: async () => {
      const response = await api.get('/coupon/customer-specific');
      return response.data as CouponListDto[];
    },
  });
}

export function useCoupon(id: string) {
  return useQuery({
    queryKey: ['coupon', id],
    queryFn: async () => {
      const response = await api.get(`/coupon/${id}`);
      return response.data as CouponListDto;
    },
    enabled: !!id,
  });
}

// Hooks for customer-specific coupon operations
export function useCustomerCoupons(customerId: string) {
  return useQuery({
    queryKey: ['customer-coupons', customerId],
    queryFn: async () => {
      const response = await api.get(`/coupon/customer/${customerId}`);
      return response.data as CouponListDto[];
    },
    enabled: !!customerId,
  });
}

export function useCustomerCouponSummary(customerId: string) {
  return useQuery({
    queryKey: ['customer-coupon-summary', customerId],
    queryFn: async () => {
      const response = await api.get(`/coupon/customer/${customerId}/summary`);
      return response.data as CouponSummaryDto;
    },
    enabled: !!customerId,
  });
}

// Mutation hooks
export function useCreateCoupon() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: any) => {
      const response = await api.post('/coupon', data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['coupons'] });
      toast.success('Kupon başarıyla oluşturuldu');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Kupon oluşturulurken bir hata oluştu');
    },
  });
}

export function useUpdateCoupon() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: any) => {
      const response = await api.put(`/coupon/${data.Dto.Id}`, data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['coupons'] });
      toast.success('Kupon başarıyla güncellendi');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Kupon güncellenirken bir hata oluştu');
    },
  });
}

export function useDeleteCoupon() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await api.delete(`/coupon/${id}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['coupons'] });
      toast.success('Kupon başarıyla silindi');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Kupon silinirken bir hata oluştu');
    },
  });
}

// Usage history hooks
export function useCouponUsageHistory(couponId: string) {
  return useQuery({
    queryKey: ['coupon-usage-history', couponId],
    queryFn: async () => {
      const response = await api.get(`/coupon/${couponId}/usage-history`);
      return response.data as CouponUsageDto[];
    },
    enabled: !!couponId,
  });
}

export function useCustomerCouponUsageHistory(customerId: string) {
  return useQuery({
    queryKey: ['customer-coupon-usage-history', customerId],
    queryFn: async () => {
      const response = await api.get(`/coupon/customer/${customerId}/usage-history`);
      return response.data as CouponUsageDto[];
    },
    enabled: !!customerId,
  });
}

// Validation hook
export function useValidateCoupon() {
  return useMutation({
    mutationFn: async (data: { couponCode: string; customerId: string }) => {
      const response = await api.post('/coupon/validate-for-customer', data);
      return response.data as CouponValidationDto;
    },
  });
}
