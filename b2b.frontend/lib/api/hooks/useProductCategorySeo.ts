import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api/client';
import { toast } from 'sonner';

// API Response wrapper type
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  errors?: string[];
}

// Types
export interface ProductCategorySeoDto {
  id: string;
  productCategoryId: string;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  canonicalUrl?: string;
  structuredData?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ProductCategorySeoCreateDto {
  productCategoryId: string;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  canonicalUrl?: string;
  structuredData?: string;
  isActive?: boolean;
}

export interface ProductCategorySeoUpdateDto extends ProductCategorySeoCreateDto {
  id: string;
}

export interface GenerateAutoSeoRequest {
  categoryName: string;
  description?: string;
}

// API functions
const productCategorySeoApi = {
  getAll: async (): Promise<ProductCategorySeoDto[]> => {
    const response = await api.get<ApiResponse<ProductCategorySeoDto[]>>('/ProductCategorySeo');
    return response?.success ? response.data : [];
  },

  getById: async (id: string): Promise<ProductCategorySeoDto | null> => {
    try {
      const response = await api.get<ApiResponse<ProductCategorySeoDto>>(`/ProductCategorySeo/${id}`);
      return response?.success ? response.data : null;
    } catch (error) {
      console.error('Error fetching category SEO by id:', error);
      return null;
    }
  },

  getByCategoryId: async (categoryId: string): Promise<ProductCategorySeoDto | null> => {
    try {
      const response = await api.get<ApiResponse<ProductCategorySeoDto>>(`/ProductCategorySeo/category/${categoryId}`);
      return response?.success ? response.data : null;
    } catch (error) {
      console.error('Error fetching category SEO by category id:', error);
      return null;
    }
  },

  createOrUpdate: async (categoryId: string, data: ProductCategorySeoCreateDto): Promise<boolean> => {
    const response = await api.post<ApiResponse<boolean>>(`/ProductCategorySeo/category/${categoryId}`, data);
    return response?.success && response.data;
  },

  update: async (id: string, data: ProductCategorySeoUpdateDto): Promise<boolean> => {
    const response = await api.put<ApiResponse<boolean>>(`/ProductCategorySeo/${id}`, data);
    return response?.success && response.data;
  },

  delete: async (id: string): Promise<boolean> => {
    const response = await api.delete<ApiResponse<boolean>>(`/ProductCategorySeo/${id}`);
    return response?.success && response.data;
  },

  deleteByCategoryId: async (categoryId: string): Promise<boolean> => {
    const response = await api.delete<ApiResponse<boolean>>(`/ProductCategorySeo/category/${categoryId}`);
    return response?.success && response.data;
  },

  generateAutoSeo: async (categoryId: string, request: GenerateAutoSeoRequest): Promise<boolean> => {
    const response = await api.post<ApiResponse<boolean>>(`/ProductCategorySeo/category/${categoryId}/generate-auto`, request);
    return response?.success && response.data;
  },
};

// Query keys
export const productCategorySeoKeys = {
  all: ['productCategorySeo'] as const,
  lists: () => [...productCategorySeoKeys.all, 'list'] as const,
  list: (filters: string) => [...productCategorySeoKeys.lists(), { filters }] as const,
  details: () => [...productCategorySeoKeys.all, 'detail'] as const,
  detail: (id: string) => [...productCategorySeoKeys.details(), id] as const,
  category: (categoryId: string) => [...productCategorySeoKeys.all, 'category', categoryId] as const,
};

// Hooks
export const useProductCategorySeos = () => {
  return useQuery({
    queryKey: productCategorySeoKeys.lists(),
    queryFn: productCategorySeoApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useProductCategorySeo = (id: string) => {
  return useQuery({
    queryKey: productCategorySeoKeys.detail(id),
    queryFn: () => productCategorySeoApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useProductCategorySeoByCategoryId = (categoryId: string) => {
  return useQuery({
    queryKey: productCategorySeoKeys.category(categoryId),
    queryFn: () => productCategorySeoApi.getByCategoryId(categoryId),
    enabled: !!categoryId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateOrUpdateProductCategorySeo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ categoryId, data }: { categoryId: string; data: ProductCategorySeoCreateDto }) =>
      productCategorySeoApi.createOrUpdate(categoryId, data),
    onSuccess: (_, { categoryId }) => {
      toast.success('SEO bilgileri başarıyla kaydedildi');
      queryClient.invalidateQueries({ queryKey: productCategorySeoKeys.category(categoryId) });
      queryClient.invalidateQueries({ queryKey: productCategorySeoKeys.lists() });
    },
    onError: (error: any) => {
      toast.error(error?.message || 'SEO bilgileri kaydedilirken hata oluştu');
    },
  });
};

export const useUpdateProductCategorySeo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ProductCategorySeoUpdateDto }) =>
      productCategorySeoApi.update(id, data),
    onSuccess: (_, { id, data }) => {
      toast.success('SEO bilgileri başarıyla güncellendi');
      queryClient.invalidateQueries({ queryKey: productCategorySeoKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: productCategorySeoKeys.category(data.productCategoryId) });
      queryClient.invalidateQueries({ queryKey: productCategorySeoKeys.lists() });
    },
    onError: (error: any) => {
      toast.error(error?.message || 'SEO bilgileri güncellenirken hata oluştu');
    },
  });
};

export const useDeleteProductCategorySeo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: productCategorySeoApi.delete,
    onSuccess: () => {
      toast.success('SEO kaydı başarıyla silindi');
      queryClient.invalidateQueries({ queryKey: productCategorySeoKeys.all });
    },
    onError: (error: any) => {
      toast.error(error?.message || 'SEO kaydı silinirken hata oluştu');
    },
  });
};

export const useDeleteProductCategorySeoByCategoryId = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: productCategorySeoApi.deleteByCategoryId,
    onSuccess: (_, categoryId) => {
      toast.success('Kategori SEO kaydı başarıyla silindi');
      queryClient.invalidateQueries({ queryKey: productCategorySeoKeys.category(categoryId) });
      queryClient.invalidateQueries({ queryKey: productCategorySeoKeys.lists() });
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Kategori SEO kaydı silinirken hata oluştu');
    },
  });
};

export const useGenerateAutoSeo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ categoryId, request }: { categoryId: string; request: GenerateAutoSeoRequest }) =>
      productCategorySeoApi.generateAutoSeo(categoryId, request),
    onSuccess: (_, { categoryId }) => {
      toast.success('Otomatik SEO bilgileri başarıyla oluşturuldu');
      queryClient.invalidateQueries({ queryKey: productCategorySeoKeys.category(categoryId) });
      queryClient.invalidateQueries({ queryKey: productCategorySeoKeys.lists() });
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Otomatik SEO bilgileri oluşturulurken hata oluştu');
    },
  });
};
