'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Loader2, Wand2, Eye, Search, Share2 } from 'lucide-react';
import {
  useProductCategorySeoByCategoryId,
  useCreateOrUpdateProductCategorySeo,
  useGenerateAutoSeo,
  type ProductCategorySeoCreateDto,
  type GenerateAutoSeoRequest,
} from '@/lib/api/hooks/useProductCategorySeo';

// Validation schema
const seoFormSchema = z.object({
  metaTitle: z.string().max(60, 'Meta title 60 karakterden uzun olamaz').optional(),
  metaDescription: z.string().max(160, 'Meta description 160 karakterden uzun olamaz').optional(),
  metaKeywords: z.string().max(255, 'Meta keywords 255 karakterden uzun olamaz').optional(),
  ogTitle: z.string().max(60, 'OG title 60 karakterden uzun olamaz').optional(),
  ogDescription: z.string().max(160, 'OG description 160 karakterden uzun olamaz').optional(),
  ogImage: z.string().url('Geçerli bir URL giriniz').optional().or(z.literal('')),
  twitterTitle: z.string().max(60, 'Twitter title 60 karakterden uzun olamaz').optional(),
  twitterDescription: z.string().max(160, 'Twitter description 160 karakterden uzun olamaz').optional(),
  twitterImage: z.string().url('Geçerli bir URL giriniz').optional().or(z.literal('')),
  canonicalUrl: z.string().url('Geçerli bir URL giriniz').optional().or(z.literal('')),
  structuredData: z.string().optional(),
  isActive: z.boolean().default(true),
});

type SeoFormValues = z.infer<typeof seoFormSchema>;

interface CategorySeoFormProps {
  categoryId: string;
  categoryName: string;
  categoryDescription?: string;
}

export default function CategorySeoForm({ categoryId, categoryName, categoryDescription }: CategorySeoFormProps) {
  const [activeTab, setActiveTab] = useState('basic');

  // Queries and mutations
  const { data: seoData, isLoading, error } = useProductCategorySeoByCategoryId(categoryId);
  const createOrUpdateMutation = useCreateOrUpdateProductCategorySeo();
  const generateAutoSeoMutation = useGenerateAutoSeo();

  // Form setup
  const form = useForm<SeoFormValues>({
    resolver: zodResolver(seoFormSchema),
    defaultValues: {
      metaTitle: '',
      metaDescription: '',
      metaKeywords: '',
      ogTitle: '',
      ogDescription: '',
      ogImage: '',
      twitterTitle: '',
      twitterDescription: '',
      twitterImage: '',
      canonicalUrl: '',
      structuredData: '',
      isActive: true,
    },
  });

  // Update form when data loads
  useEffect(() => {
    if (seoData) {
      form.reset({
        metaTitle: seoData.metaTitle || '',
        metaDescription: seoData.metaDescription || '',
        metaKeywords: seoData.metaKeywords || '',
        ogTitle: seoData.ogTitle || '',
        ogDescription: seoData.ogDescription || '',
        ogImage: seoData.ogImage || '',
        twitterTitle: seoData.twitterTitle || '',
        twitterDescription: seoData.twitterDescription || '',
        twitterImage: seoData.twitterImage || '',
        canonicalUrl: seoData.canonicalUrl || '',
        structuredData: seoData.structuredData || '',
        isActive: seoData.isActive,
      });
    }
  }, [seoData, form]);

  // Form submission
  const onSubmit = async (values: SeoFormValues) => {
    const data: ProductCategorySeoCreateDto = {
      productCategoryId: categoryId,
      ...values,
    };

    await createOrUpdateMutation.mutateAsync({ categoryId, data });
  };

  // Generate auto SEO
  const handleGenerateAutoSeo = async () => {
    const request: GenerateAutoSeoRequest = {
      categoryName,
      description: categoryDescription,
    };

    await generateAutoSeoMutation.mutateAsync({ categoryId, request });
  };

  // Character count helpers
  const getCharacterCount = (value: string | undefined, maxLength: number) => {
    const length = value?.length || 0;
    const isOverLimit = length > maxLength;
    return (
      <span className={`text-xs ${isOverLimit ? 'text-red-500' : 'text-muted-foreground'}`}>
        {length}/{maxLength}
      </span>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">SEO bilgileri yükleniyor...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              SEO Ayarları
            </CardTitle>
            <CardDescription>
              {categoryName} kategorisi için SEO meta bilgilerini yönetin
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleGenerateAutoSeo}
              disabled={generateAutoSeoMutation.isPending}
            >
              {generateAutoSeoMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Wand2 className="h-4 w-4" />
              )}
              Otomatik Oluştur
            </Button>
            {seoData && (
              <Badge variant={seoData.isActive ? 'default' : 'secondary'}>
                {seoData.isActive ? 'Aktif' : 'Pasif'}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic" className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  Temel SEO
                </TabsTrigger>
                <TabsTrigger value="social" className="flex items-center gap-2">
                  <Share2 className="h-4 w-4" />
                  Sosyal Medya
                </TabsTrigger>
                <TabsTrigger value="advanced" className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  Gelişmiş
                </TabsTrigger>
                <TabsTrigger value="preview">Önizleme</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <FormField
                  control={form.control}
                  name="metaTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meta Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Kategori için meta title" {...field} />
                      </FormControl>
                      <div className="flex justify-between">
                        <FormDescription>
                          Arama motorlarında görünecek başlık
                        </FormDescription>
                        {getCharacterCount(field.value, 60)}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="metaDescription"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meta Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Kategori için meta açıklama"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <div className="flex justify-between">
                        <FormDescription>
                          Arama sonuçlarında görünecek açıklama
                        </FormDescription>
                        {getCharacterCount(field.value, 160)}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="metaKeywords"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meta Keywords</FormLabel>
                      <FormControl>
                        <Input placeholder="anahtar, kelime, virgül, ile, ayrılmış" {...field} />
                      </FormControl>
                      <div className="flex justify-between">
                        <FormDescription>
                          Virgül ile ayrılmış anahtar kelimeler
                        </FormDescription>
                        {getCharacterCount(field.value, 255)}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="social" className="space-y-4">
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Open Graph (Facebook, LinkedIn)</h4>

                  <FormField
                    control={form.control}
                    name="ogTitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>OG Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Facebook/LinkedIn için başlık" {...field} />
                        </FormControl>
                        <div className="flex justify-between">
                          <FormDescription>
                            Sosyal medyada paylaşıldığında görünecek başlık
                          </FormDescription>
                          {getCharacterCount(field.value, 60)}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="ogDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>OG Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Facebook/LinkedIn için açıklama"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <div className="flex justify-between">
                          <FormDescription>
                            Sosyal medyada paylaşıldığında görünecek açıklama
                          </FormDescription>
                          {getCharacterCount(field.value, 160)}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="ogImage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>OG Image URL</FormLabel>
                        <FormControl>
                          <Input placeholder="https://example.com/image.jpg" {...field} />
                        </FormControl>
                        <FormDescription>
                          Sosyal medyada paylaşıldığında görünecek görsel URL'i
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Twitter Cards</h4>

                  <FormField
                    control={form.control}
                    name="twitterTitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Twitter Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Twitter için başlık" {...field} />
                        </FormControl>
                        <div className="flex justify-between">
                          <FormDescription>
                            Twitter'da paylaşıldığında görünecek başlık
                          </FormDescription>
                          {getCharacterCount(field.value, 60)}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="twitterDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Twitter Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Twitter için açıklama"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <div className="flex justify-between">
                          <FormDescription>
                            Twitter'da paylaşıldığında görünecek açıklama
                          </FormDescription>
                          {getCharacterCount(field.value, 160)}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="twitterImage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Twitter Image URL</FormLabel>
                        <FormControl>
                          <Input placeholder="https://example.com/image.jpg" {...field} />
                        </FormControl>
                        <FormDescription>
                          Twitter'da paylaşıldığında görünecek görsel URL'i
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4">
                <FormField
                  control={form.control}
                  name="canonicalUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Canonical URL</FormLabel>
                      <FormControl>
                        <Input placeholder="https://example.com/kategori/kategori-adi" {...field} />
                      </FormControl>
                      <FormDescription>
                        Bu sayfanın asıl URL'i (duplicate content önleme için)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="structuredData"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Structured Data (JSON-LD)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='{"@context": "https://schema.org", "@type": "CategoryPage", ...}'
                          className="min-h-[120px] font-mono text-sm"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Schema.org structured data (JSON-LD formatında)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">SEO Aktif</FormLabel>
                        <FormDescription>
                          Bu kategori için SEO ayarlarını etkinleştir
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="preview" className="space-y-4">
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Arama Motoru Önizlemesi</h4>
                  <div className="border rounded-lg p-4 bg-muted/50">
                    <div className="text-blue-600 text-lg hover:underline cursor-pointer">
                      {form.watch('metaTitle') || categoryName}
                    </div>
                    <div className="text-green-700 text-sm">
                      example.com/kategori/{categoryName.toLowerCase().replace(/\s+/g, '-')}
                    </div>
                    <div className="text-gray-600 text-sm mt-1">
                      {form.watch('metaDescription') || `${categoryName} kategorisindeki ürünleri keşfedin.`}
                    </div>
                  </div>

                  <h4 className="text-sm font-medium">Sosyal Medya Önizlemesi</h4>
                  <div className="border rounded-lg p-4 bg-muted/50">
                    <div className="bg-gray-200 h-32 rounded mb-2 flex items-center justify-center text-gray-500">
                      {form.watch('ogImage') ? 'Görsel Yüklendi' : 'Görsel Yok'}
                    </div>
                    <div className="font-medium">
                      {form.watch('ogTitle') || form.watch('metaTitle') || categoryName}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {form.watch('ogDescription') || form.watch('metaDescription') || `${categoryName} kategorisindeki ürünleri keşfedin.`}
                    </div>
                    <div className="text-xs text-gray-500 mt-2">
                      example.com
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex justify-end gap-2">
              <Button
                type="submit"
                disabled={createOrUpdateMutation.isPending}
              >
                {createOrUpdateMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : null}
                {seoData ? 'Güncelle' : 'Kaydet'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
