'use client';

import { useState, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { SystemSettingsDto, GroupedSystemSettings, SystemSettingsDataType } from '@/types/system-settings';
import { useGroupedSystemSettings, useCreateSystemSetting, useUpdateSystemSetting, useDeleteSystemSetting } from '@/lib/api/hooks/useSystemSettings';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  ChevronDown,
  ChevronRight,
  Edit,
  Save,
  X,
  Plus,
  Trash2,
  Search,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';

interface SystemSettingsListClientProps {
  initialSettings: SystemSettingsDto[];
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

interface EditingState {
  [key: string]: {
    value: string;
    description: string;
    category: string;
    dataType: string;
  };
}

export default function SystemSettingsListClient({
  initialSettings,
  canCreate,
  canUpdate,
  canDelete
}: SystemSettingsListClientProps) {
  const t = useTranslations('systemSettings');
  const commonT = useTranslations('common');

  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [editingSettings, setEditingSettings] = useState<EditingState>({});
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newSetting, setNewSetting] = useState({
    key: '',
    value: '',
    description: '',
    category: '',
    dataType: SystemSettingsDataType.STRING,
    isSystem: false
  });

  // Use the hook to get grouped settings
  const { data: groupedSettings, isLoading, error } = useGroupedSystemSettings();

  // Mutations
  const createMutation = useCreateSystemSetting();
  const updateMutation = useUpdateSystemSetting();
  const deleteMutation = useDeleteSystemSetting();

  // Filter settings based on search term
  const filteredGroupedSettings = useMemo(() => {
    if (!groupedSettings || !searchTerm) return groupedSettings;

    const filtered: GroupedSystemSettings = {};

    Object.entries(groupedSettings).forEach(([category, settings]) => {
      const filteredSettings = settings.filter(setting =>
        setting.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
        setting.value.toLowerCase().includes(searchTerm.toLowerCase()) ||
        setting.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        setting.category?.toLowerCase().includes(searchTerm.toLowerCase())
      );

      if (filteredSettings.length > 0) {
        filtered[category] = filteredSettings;
      }
    });

    return filtered;
  }, [groupedSettings, searchTerm]);

  // Toggle category expansion
  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  // Start editing a setting
  const startEditing = (setting: SystemSettingsDto) => {
    if (!canUpdate) return;

    setEditingSettings({
      ...editingSettings,
      [setting.id]: {
        value: setting.value,
        description: setting.description || '',
        category: setting.category || '',
        dataType: setting.dataType
      }
    });
  };

  // Cancel editing
  const cancelEditing = (settingId: string) => {
    const newEditing = { ...editingSettings };
    delete newEditing[settingId];
    setEditingSettings(newEditing);
  };

  // Save edited setting
  const saveEditing = async (settingId: string) => {
    const editData = editingSettings[settingId];
    if (!editData) return;

    try {
      await updateMutation.mutateAsync({
        id: settingId,
        value: editData.value,
        description: editData.description,
        category: editData.category,
        dataType: editData.dataType
      });

      cancelEditing(settingId);
      toast.success(t('messages.updateSuccess'));
    } catch (error) {
      toast.error(t('messages.updateError'));
    }
  };

  // Delete setting
  const deleteSetting = async (setting: SystemSettingsDto) => {
    if (!canDelete || setting.isSystem) {
      toast.error(setting.isSystem ? t('actions.systemSettingCannotDelete') : t('messages.noPermission'));
      return;
    }

    if (confirm(t('actions.deleteConfirm'))) {
      try {
        await deleteMutation.mutateAsync(setting.id);
        toast.success(t('messages.deleteSuccess'));
      } catch (error) {
        toast.error(t('messages.deleteError'));
      }
    }
  };

  // Create new setting
  const createNewSetting = async () => {
    if (!canCreate) return;

    try {
      await createMutation.mutateAsync(newSetting);
      setIsAddingNew(false);
      setNewSetting({
        key: '',
        value: '',
        description: '',
        category: '',
        dataType: SystemSettingsDataType.STRING,
        isSystem: false
      });
      toast.success(t('messages.createSuccess'));
    } catch (error) {
      toast.error(t('messages.createError'));
    }
  };

  if (isLoading) {
    return <div className="text-center py-8">{t('messages.loadingSettings')}</div>;
  }

  if (error) {
    return <div className="text-center py-8 text-red-500">Error loading settings</div>;
  }

  return (
    <div className="space-y-6">
      {/* Search and Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder={t('placeholders.searchSettings')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {canCreate && (
          <Button
            onClick={() => setIsAddingNew(true)}
            disabled={isAddingNew}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {t('actions.addNew')}
          </Button>
        )}
      </div>

      {/* Add New Setting Form */}
      {isAddingNew && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              {t('add')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">{t('fields.key')}</label>
                <Input
                  value={newSetting.key}
                  onChange={(e) => setNewSetting({ ...newSetting, key: e.target.value })}
                  placeholder={t('placeholders.key')}
                />
              </div>
              <div>
                <label className="text-sm font-medium">{t('fields.category')}</label>
                <Input
                  value={newSetting.category}
                  onChange={(e) => setNewSetting({ ...newSetting, category: e.target.value })}
                  placeholder={t('placeholders.category')}
                />
              </div>
              <div>
                <label className="text-sm font-medium">{t('fields.dataType')}</label>
                <Select
                  value={newSetting.dataType}
                  onValueChange={(value) => setNewSetting({ ...newSetting, dataType: value as SystemSettingsDataType })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={SystemSettingsDataType.STRING}>{t('dataTypes.string')}</SelectItem>
                    <SelectItem value={SystemSettingsDataType.INT}>{t('dataTypes.int')}</SelectItem>
                    <SelectItem value={SystemSettingsDataType.BOOL}>{t('dataTypes.bool')}</SelectItem>
                    <SelectItem value={SystemSettingsDataType.DECIMAL}>{t('dataTypes.decimal')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium">{t('fields.value')}</label>
                <Input
                  value={newSetting.value}
                  onChange={(e) => setNewSetting({ ...newSetting, value: e.target.value })}
                  placeholder={t('placeholders.value')}
                />
              </div>
            </div>
            <div>
              <label className="text-sm font-medium">{t('fields.description')}</label>
              <Textarea
                value={newSetting.description}
                onChange={(e) => setNewSetting({ ...newSetting, description: e.target.value })}
                placeholder={t('placeholders.description')}
                rows={2}
              />
            </div>
            <div className="flex gap-2">
              <Button onClick={createNewSetting} disabled={!newSetting.key || !newSetting.value}>
                <Save className="h-4 w-4 mr-2" />
                {commonT('actions.save')}
              </Button>
              <Button variant="outline" onClick={() => setIsAddingNew(false)}>
                <X className="h-4 w-4 mr-2" />
                {commonT('actions.cancel')}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Settings by Category */}
      {filteredGroupedSettings && Object.entries(filteredGroupedSettings).map(([category, settings]) => (
        <Card key={category}>
          <Collapsible
            open={expandedCategories.has(category)}
            onOpenChange={() => toggleCategory(category)}
          >
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    {category === 'Uncategorized' ? t('uncategorized') : category}
                    <Badge variant="secondary">{settings.length}</Badge>
                  </div>
                  {expandedCategories.has(category) ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>

            <CollapsibleContent>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t('fields.key')}</TableHead>
                      <TableHead>{t('fields.value')}</TableHead>
                      <TableHead>{t('fields.description')}</TableHead>
                      <TableHead>{t('fields.dataType')}</TableHead>
                      <TableHead>{t('fields.isSystem')}</TableHead>
                      <TableHead className="text-right">{commonT('actions.title')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {settings.map((setting) => {
                      const isEditing = editingSettings[setting.id];

                      return (
                        <TableRow key={setting.id}>
                          <TableCell className="font-medium">{setting.key}</TableCell>
                          <TableCell>
                            {isEditing ? (
                              <Input
                                value={isEditing.value}
                                onChange={(e) => setEditingSettings({
                                  ...editingSettings,
                                  [setting.id]: { ...isEditing, value: e.target.value }
                                })}
                                className="min-w-[200px]"
                              />
                            ) : (
                              <span className="break-all">{setting.value}</span>
                            )}
                          </TableCell>
                          <TableCell>
                            {isEditing ? (
                              <Textarea
                                value={isEditing.description}
                                onChange={(e) => setEditingSettings({
                                  ...editingSettings,
                                  [setting.id]: { ...isEditing, description: e.target.value }
                                })}
                                rows={2}
                                className="min-w-[200px]"
                              />
                            ) : (
                              <span className="text-sm text-muted-foreground">
                                {setting.description || '-'}
                              </span>
                            )}
                          </TableCell>
                          <TableCell>
                            {isEditing ? (
                              <Select
                                value={isEditing.dataType}
                                onValueChange={(value) => setEditingSettings({
                                  ...editingSettings,
                                  [setting.id]: { ...isEditing, dataType: value }
                                })}
                              >
                                <SelectTrigger className="w-[120px]">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="string">{t('dataTypes.string')}</SelectItem>
                                  <SelectItem value="int">{t('dataTypes.int')}</SelectItem>
                                  <SelectItem value="bool">{t('dataTypes.bool')}</SelectItem>
                                  <SelectItem value="decimal">{t('dataTypes.decimal')}</SelectItem>
                                </SelectContent>
                              </Select>
                            ) : (
                              <Badge variant="outline">
                                {t(`dataTypes.${setting.dataType}` as any)}
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge variant={setting.isSystem ? "default" : "secondary"}>
                              {setting.isSystem ? commonT('actions.yes') : commonT('actions.no')}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              {isEditing ? (
                                <>
                                  <Button
                                    size="sm"
                                    onClick={() => saveEditing(setting.id)}
                                    disabled={updateMutation.isPending}
                                  >
                                    <Save className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => cancelEditing(setting.id)}
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </>
                              ) : (
                                <>
                                  {canUpdate && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => startEditing(setting)}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  )}
                                  {canDelete && !setting.isSystem && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => deleteSetting(setting)}
                                      disabled={deleteMutation.isPending}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  )}
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>
      ))}

      {(!filteredGroupedSettings || Object.keys(filteredGroupedSettings).length === 0) && (
        <div className="text-center py-8 text-muted-foreground">
          {t('noSettings')}
        </div>
      )}
    </div>
  );
}
