'use server';

import { Suspense } from 'react';
import SystemSettingsListClient from './SystemSettingsListClient';
import { SystemSettingsDto } from '@/types/system-settings';
import api from '@/lib/api/client';

interface SystemSettingsListServerProps {
    canRead: boolean;
    canCreate: boolean;
    canUpdate: boolean;
    canDelete: boolean;
}

// This would typically fetch data from your API
async function getSystemSettings(): Promise<SystemSettingsDto[]> {
    try {
        // Try to get data from API using the existing api client
        const response = await api.get<SystemSettingsDto[]>("/systemsettings");

        // Transform backend data to frontend format if needed
        return response.map((setting: SystemSettingsDto) => ({
            id: setting.id,
            key: setting.key,
            value: setting.value,
            description: setting.description,
            category: setting.category,
            dataType: setting.dataType,
            isSystem: setting.isSystem,
            isActive: setting.isActive,
            isDeleted: setting.isDeleted,
            createdAt: setting.createdAt,
            updatedAt: setting.updatedAt,
        }));
    } catch (error) {
        console.error("SystemSettings API çağrısı başarısız oldu", error);
        // If API call fails, return empty array
        return [];
    }
}

export default async function SystemSettingsListServer({
    canRead,
    canCreate,
    canUpdate,
    canDelete
}: SystemSettingsListServerProps) {
    if (!canRead) {
        return <div>Bu sayfayı görüntüleme yetkiniz yok.</div>;
    }

    const settings = await getSystemSettings();

    return (
        <Suspense fallback={<div>Sistem ayarları yükleniyor...</div>}>
            <SystemSettingsListClient
                initialSettings={settings}
                canCreate={canCreate}
                canUpdate={canUpdate}
                canDelete={canDelete}
            />
        </Suspense>
    );
}
