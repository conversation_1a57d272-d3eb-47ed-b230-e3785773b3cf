import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { hasServerPermission } from "@/lib/auth/server-permissions";
import { PermissionProvider } from "@/components/auth/permission-provider";
import SystemSettingsListServer from "./components/SystemSettingsListServer";
import PageHeaderServer from "../../components/PageHeaderServer";

export default async function SystemSettingsPage() {
  // Server-side permission check - redirect if no access
  const canRead = await hasServerPermission("settings", "read");
  if (!canRead) {
    redirect("/admin/dashboard");
  }

  const t = await getTranslations("systemSettings");

  return (
    <div className="space-y-6">
      <PermissionProvider resource="settings">
        {({ read, create, update, delete: canDelete }) => (
          <>
            <PageHeaderServer
              title={t("title")}
              description={t("list")}
              actions={[]}
            />
            <SystemSettingsListServer
              canRead={read}
              canCreate={create}
              canUpdate={update}
              canDelete={canDelete}
            />
          </>
        )}
      </PermissionProvider>
    </div>
  );
}
