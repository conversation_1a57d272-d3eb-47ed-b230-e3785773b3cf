import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import CouponsListServer from './components/CouponsListServer';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('admin.coupons');
  
  return {
    title: t('title'),
    description: t('description'),
  };
}

export default function CouponsPage() {
  return (
    <div className="space-y-6">
      <CouponsListServer />
    </div>
  );
}
