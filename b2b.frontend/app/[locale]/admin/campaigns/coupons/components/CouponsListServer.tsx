import { getTranslations } from 'next-intl/server';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import CouponsListClient from './CouponsListClient';

export default async function CouponsListServer() {
  const t = await getTranslations('admin.coupons');

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
          <p className="text-muted-foreground">{t('description')}</p>
        </div>
      </div>

      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">{t('tabs.all')}</TabsTrigger>
          <TabsTrigger value="general">{t('tabs.general')}</TabsTrigger>
          <TabsTrigger value="customer-specific">{t('tabs.customerSpecific')}</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('allCoupons.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CouponsListClient type="all" />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('generalCoupons.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CouponsListClient type="general" />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customer-specific" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('customerSpecificCoupons.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CouponsListClient type="customer-specific" />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
