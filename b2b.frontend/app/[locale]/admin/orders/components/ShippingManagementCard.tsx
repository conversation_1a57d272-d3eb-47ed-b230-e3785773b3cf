'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Order, OrderStatus, Shipment } from '@/types/order';
import { useCreateShipmentWithCarrier, useUpdateTrackingNumber, useShippingCarriers } from '@/lib/api/hooks/useShipping';
import { toast } from 'sonner';
import { Truck, Package, Edit3, Printer } from 'lucide-react';
import ShippingLabelModal from '@/components/shipping/ShippingLabelModal';
import { Separator } from '@radix-ui/react-separator';

interface ShippingManagementCardProps {
  order: Order;
  shipments: Shipment[];
  canUpdate: boolean;
}

export default function ShippingManagementCard({
  order,
  shipments,
  canUpdate,
}: ShippingManagementCardProps) {
  const [editingShipmentId, setEditingShipmentId] = useState<string | null>(null);
  const [editTrackingNumber, setEditTrackingNumber] = useState('');
  const [selectedCarrier, setSelectedCarrier] = useState<string>('');
  const [showShippingLabel, setShowShippingLabel] = useState(false);

  const createShipmentMutation = useCreateShipmentWithCarrier();
  const updateTrackingMutation = useUpdateTrackingNumber();
  const { data: carriers = [], isLoading: carriersLoading } = useShippingCarriers();

  // Sipariş durumu kontrolü - sadece Beklemede veya İşleniyor durumunda kargoya verilebilir
  const canCreateShipment = order.status === OrderStatus.Pending || order.status === OrderStatus.Processing;

  // Mevcut kargo var mı kontrolü
  const hasShipment = shipments.length > 0;

  const handleCreateShipment = async () => {
    if (!order.address || !order.customer) {
      toast.error('Sipariş adresi veya müşteri bilgisi eksik');
      return;
    }

    if (!selectedCarrier) {
      toast.error('Lütfen bir kargo firması seçin');
      return;
    }

    // Zorunlu alanları kontrol et
    if (!order.customer.nameSurname) {
      toast.error('Müşteri adı soyadı eksik');
      return;
    }

    if (!order.customer.phoneNumber) {
      toast.error('Müşteri telefon numarası eksik');
      return;
    }

    if (!order.address.line1 && !order.address.addressLine) {
      toast.error('Teslimat adresi eksik');
      return;
    }

    if (!order.address.city) {
      toast.error('Şehir bilgisi eksik');
      return;
    }

    if (!order.address.district) {
      toast.error('İlçe bilgisi eksik');
      return;
    }

    try {
      // Address bilgisini oluştur (line1 + line2 birleşimi)
      const fullAddress = [
        order.address.line1 || order.address.addressLine,
        order.address.line2
      ].filter(Boolean).join(', ');

      const shipmentData = {
        carrierShortCode: selectedCarrier,
        recipientName: order.customer.nameSurname,
        recipientPhone: order.customer.phoneNumber,
        recipientEmail: order.customer.email || undefined,
        address: fullAddress,
        city: order.address.city,
        district: order.address.district,
        postalCode: order.address.postalCode || undefined,
        weight: 1.0, // Varsayılan ağırlık - daha sonra sipariş ağırlığından hesaplanabilir
        declaredValue: order.totalAmount,
        specialInstructions: order.notes || undefined,
      };

      await createShipmentMutation.mutateAsync({
        orderId: order.id,
        data: shipmentData,
      });

      toast.success('Kargo başarıyla oluşturuldu');
      setSelectedCarrier(''); // Seçimi temizle

      // Kargo etiketi modalını aç
      setShowShippingLabel(true);
    } catch (error) {
      toast.error('Kargo oluşturulurken bir hata oluştu');
      console.error('Shipment creation error:', error);
    }
  };

  const handleUpdateTrackingNumber = async (shipmentId: string) => {
    if (!editTrackingNumber.trim()) {
      toast.error('Takip numarası boş olamaz');
      return;
    }

    try {
      await updateTrackingMutation.mutateAsync({
        id: shipmentId,
        trackingNumber: editTrackingNumber,
      });

      toast.success('Takip numarası başarıyla güncellendi');
      setEditingShipmentId(null);
      setEditTrackingNumber('');
    } catch (error) {
      toast.error('Takip numarası güncellenirken bir hata oluştu');
      console.error('Tracking number update error:', error);
    }
  };

  const startEditingTrackingNumber = (shipment: Shipment) => {
    setEditingShipmentId(shipment.id);
    setEditTrackingNumber(shipment.trackingNumber);
  };

  const cancelEditingTrackingNumber = () => {
    setEditingShipmentId(null);
    setEditTrackingNumber('');
  };

  return (
    <Card className="dark:bg-gray-800 dark:border-gray-700">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2 dark:text-white">
          <Truck className="w-5 h-5" />
          Kargo Yönetimi
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Mevcut Kargolar */}
        {hasShipment && (
          <div className="space-y-3">
            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Mevcut Kargolar
            </Label>
            {shipments.map((shipment) => (
              <div
                key={shipment.id}
                className="p-3 border rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Package className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {shipment.carrier}
                    </span>
                  </div>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date(shipment.createdAt).toLocaleDateString('tr-TR')}
                  </span>
                </div>

                {/* Takip Numarası */}
                <div className="space-y-2">
                  <Label className="text-xs text-gray-600 dark:text-gray-400">
                    Takip Numarası
                  </Label>
                  {editingShipmentId === shipment.id ? (
                    <div className="flex items-center gap-2">
                      <Input
                        value={editTrackingNumber}
                        onChange={(e) => setEditTrackingNumber(e.target.value)}
                        placeholder="Takip numarası girin"
                        className="text-sm dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                      />
                      <Button
                        size="sm"
                        onClick={() => handleUpdateTrackingNumber(shipment.id)}
                        disabled={updateTrackingMutation.isPending}
                        className="px-2"
                      >
                        Kaydet
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={cancelEditingTrackingNumber}
                        className="px-2"
                      >
                        İptal
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-mono text-gray-900 dark:text-white">
                        {shipment.trackingNumber || 'Henüz atanmadı'}
                      </span>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setShowShippingLabel(true)}
                          className="p-1 h-auto"
                          title="Kargo Etiketi Yazdır"
                        >
                          <Printer className="w-3 h-3" />
                        </Button>
                        {canUpdate && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => startEditingTrackingNumber(shipment)}
                            className="p-1 h-auto"
                          >
                            <Edit3 className="w-3 h-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                <Separator />
                <div className="space-y-2">
                  <Label className="text-xs text-gray-600 dark:text-gray-400">
                    Kargo Kodu
                  </Label>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {shipment.cargoKey || 'Henüz atanmadı'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Yeni Kargo Oluşturma */}
        {canUpdate && canCreateShipment && !hasShipment && (
          <div className="space-y-3">
            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Kargo Oluştur
            </Label>
            <div className="p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg space-y-4">
              {/* Kargo Firması Seçimi */}
              <div className="space-y-2">
                <Label className="text-sm text-gray-600 dark:text-gray-400">
                  Kargo Firması
                </Label>
                <Select value={selectedCarrier} onValueChange={setSelectedCarrier}>
                  <SelectTrigger className="dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <SelectValue placeholder={carriersLoading ? "Yükleniyor..." : "Kargo firması seçin"} />
                  </SelectTrigger>
                  <SelectContent>
                    {carriers.map((carrier) => (
                      <SelectItem key={carrier.shortCode} value={carrier.shortCode}>
                        <div className="flex items-center gap-2">
                          {carrier.logoUrl && carrier.logoUrl.trim() !== '' && (
                            <Image
                              src={carrier.logoUrl}
                              alt={carrier.name}
                              width={16}
                              height={16}
                              className="object-contain"
                              onError={(e) => {
                                // Logo yüklenemezse gizle
                                e.currentTarget.style.display = 'none';
                              }}
                            />
                          )}
                          <span>{carrier.name}</span>
                          {carrier.shortCode.includes('TEST') && (
                            <span className="text-xs bg-yellow-100 text-yellow-800 px-1 rounded">
                              TEST
                            </span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Kargo Oluşturma Butonu */}
              <div className="text-center space-y-3">
                <Package className="w-8 h-8 text-gray-400 mx-auto" />
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Bu sipariş için henüz kargo oluşturulmamış
                </p>
                <Button
                  onClick={handleCreateShipment}
                  disabled={createShipmentMutation.isPending || !selectedCarrier || carriersLoading}
                  className="w-full"
                >
                  {createShipmentMutation.isPending ? 'Oluşturuluyor...' : 'Kargoya Ver'}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Durum Mesajları */}
        {!canCreateShipment && (
          <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              Kargo oluşturmak için sipariş durumu "Beklemede" veya "İşleniyor" olmalıdır.
            </p>
          </div>
        )}
      </CardContent>

      {/* Kargo Etiketi Modal */}
      <ShippingLabelModal
        isOpen={showShippingLabel}
        onClose={() => setShowShippingLabel(false)}
        orderId={order.id}
      />
    </Card>
  );
}
