'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Plus,
  Gift,
  Calendar as CalendarIcon,
  Loader2,
  MoreHorizontal,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Percent,
  DollarSign
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import {
  useCustomerCoupons,
  useCustomerCouponSummary,
  useCreateCoupon,
  useUpdateCoupon,
  useDeleteCoupon,
  CouponListDto
} from '@/lib/api/hooks/useCoupons';

// Form validation schema
const couponSchema = z.object({
  couponCode: z.string().min(3, 'Kupon kodu en az 3 karakter olmalıdır').max(50),
  discountType: z.enum(['Yüzde', 'Sabit Miktar']),
  discountValue: z.number().min(0.01, 'İndirim değeri 0\'dan büyük olmalıdır'),
  expirationDate: z.date().optional(),
  usageLimitPerCustomer: z.number().min(1, 'Müşteri başına kullanım limiti en az 1 olmalıdır'),
  description: z.string().optional(),
  isActive: z.boolean(),
});

type CouponFormData = z.infer<typeof couponSchema>;

interface CustomerCouponsTabProps {
  customerId: string;
}

export function CustomerCouponsTab({ customerId }: CustomerCouponsTabProps) {
  const t = useTranslations("admin.coupons");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingCoupon, setEditingCoupon] = useState<CouponListDto | null>(null);

  const { data: coupons, isLoading, refetch } = useCustomerCoupons(customerId);
  const { data: couponSummary } = useCustomerCouponSummary(customerId);
  const createCouponMutation = useCreateCoupon();
  const updateCouponMutation = useUpdateCoupon();
  const deleteCouponMutation = useDeleteCoupon();

  const form = useForm<CouponFormData>({
    resolver: zodResolver(couponSchema),
    defaultValues: {
      discountType: 'Yüzde',
      usageLimitPerCustomer: 1,
      isActive: true,
    },
  });

  const editForm = useForm<CouponFormData>({
    resolver: zodResolver(couponSchema),
    defaultValues: {
      discountType: 'Yüzde',
      usageLimitPerCustomer: 1,
      isActive: true,
    },
  });

  const onSubmit = async (data: CouponFormData) => {
    try {
      const couponData = {
        Dto: {
          CouponCode: data.couponCode,
          CouponType: 0, // CustomerSpecific = 0
          CustomerId: customerId,
          DiscountType: data.discountType === 'Yüzde' ? 1 : 0, // Percentage=1, Fixed=0
          DiscountAmount: data.discountValue,
          TotalUsageLimit: null,
          UsageLimitPerCustomer: data.usageLimitPerCustomer,
          ExpirationDate: data.expirationDate?.toISOString() || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          Description: data.description || null,
        }
      };

      await createCouponMutation.mutateAsync(couponData);
      form.reset();
      setIsAddDialogOpen(false);
      refetch();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const onEditSubmit = async (data: CouponFormData) => {
    if (!editingCoupon) return;

    try {
      const updateData = {
        Dto: {
          Id: editingCoupon.id,
          CouponCode: data.couponCode,
          CouponType: 0, // CustomerSpecific = 0
          CustomerId: customerId,
          DiscountType: data.discountType === 'Yüzde' ? 1 : 0, // Percentage=1, Fixed=0
          DiscountAmount: data.discountValue,
          TotalUsageLimit: null,
          UsageLimitPerCustomer: data.usageLimitPerCustomer,
          ExpirationDate: data.expirationDate?.toISOString() || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          Description: data.description || null,
        }
      };

      await updateCouponMutation.mutateAsync(updateData);
      editForm.reset();
      setIsEditDialogOpen(false);
      setEditingCoupon(null);
      refetch();
    } catch (_error) {
      // Error is handled by the mutation
    }
  };

  const handleEdit = (coupon: CouponListDto) => {
    setEditingCoupon(coupon);
    editForm.reset({
      couponCode: coupon.couponCode,
      discountType: coupon.discountTypeText === 'Yüzde' ? 'Yüzde' : 'Sabit Miktar',
      discountValue: coupon.discountAmount,
      usageLimitPerCustomer: coupon.usageLimitPerCustomer,
      expirationDate: coupon.expirationDate ? new Date(coupon.expirationDate) : undefined,
      description: coupon.description || '',
      isActive: coupon.isActive,
    });
    setIsEditDialogOpen(true);
  };

  const handleDelete = async (couponId: string) => {
    try {
      await deleteCouponMutation.mutateAsync(couponId);
      refetch();
    } catch (_error) {
      // Error is handled by the mutation
    }
  };

  const generateCouponCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    form.setValue('couponCode', result);
  };

  const generateEditCouponCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    editForm.setValue('couponCode', result);
  };

  const getStatusBadge = (coupon: CouponListDto) => {
    if (!coupon.isActive) {
      return <Badge variant="secondary">{t('status.inactive')}</Badge>;
    }

    if (coupon.expirationDate && new Date(coupon.expirationDate) < new Date()) {
      return <Badge variant="destructive">{t('status.expired')}</Badge>;
    }

    return <Badge variant="default">{t('status.active')}</Badge>;
  };

  const getDiscountDisplay = (coupon: CouponListDto) => {
    return coupon.discountTypeText === 'Yüzde'
      ? `%${coupon.discountAmount}`
      : `₺${coupon.discountAmount}`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      {couponSummary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Kupon</CardTitle>
              <Gift className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{couponSummary.totalCoupons}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Aktif Kuponlar</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{couponSummary.activeCoupons}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Kullanılan</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{couponSummary.usedCoupons}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Süresi Dolan</CardTitle>
              <CalendarIcon className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{couponSummary.expiredCoupons}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Coupons Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Müşteri Kuponları</CardTitle>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  {t('actions.addCoupon')}
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>{t('addModal.title')}</DialogTitle>
                  <DialogDescription>{t('addModal.description')}</DialogDescription>
                </DialogHeader>

                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="couponCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.couponCode')}</FormLabel>
                            <div className="flex space-x-2">
                              <FormControl>
                                <Input {...field} placeholder="KUPON123" />
                              </FormControl>
                              <Button type="button" variant="outline" onClick={generateCouponCode}>
                                {t('form.generate')}
                              </Button>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="discountType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.discountType')}</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="Percentage">{t('discountType.percentage')}</SelectItem>
                                <SelectItem value="FixedAmount">{t('discountType.fixedAmount')}</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="discountValue"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {form.watch('discountType') === 'Yüzde'
                                ? t('form.discountPercentage')
                                : t('form.discountAmount')
                              }
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="usageLimitPerCustomer"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.usageLimitPerCustomer')}</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="expirationDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>{t('form.expirationDate')}</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>{t('form.selectDate')}</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => date < new Date()}
                                autoFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>{t('form.expirationDateDescription')}</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('form.description')}</FormLabel>
                          <FormControl>
                            <Textarea {...field} placeholder={t('form.descriptionPlaceholder')} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="isActive"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">{t('form.isActive')}</FormLabel>
                            <FormDescription>{t('form.isActiveDescription')}</FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-end space-x-2">
                      <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                        {t('actions.cancel')}
                      </Button>
                      <Button type="submit" disabled={createCouponMutation.isPending}>
                        {createCouponMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        {t('actions.save')}
                      </Button>
                    </div>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {coupons && coupons.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('table.couponCode')}</TableHead>
                    <TableHead>{t('table.discount')}</TableHead>
                    <TableHead>{t('table.usage')}</TableHead>
                    <TableHead>{t('table.expiration')}</TableHead>
                    <TableHead>{t('table.status')}</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {coupons.map((coupon) => (
                    <TableRow key={coupon.id}>
                      <TableCell className="font-medium">{coupon.couponCode}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <span>{getDiscountDisplay(coupon)}</span>
                        </div>
                      </TableCell>
                      <TableCell>{coupon.totalUsageCount}/{coupon.usageLimitPerCustomer}</TableCell>
                      <TableCell>
                        {coupon.expirationDate ? (
                          <div className="flex items-center space-x-1">
                            <CalendarIcon className="h-3 w-3" />
                            <span>{new Date(coupon.expirationDate).toLocaleDateString()}</span>
                          </div>
                        ) : (
                          '-'
                        )}
                      </TableCell>
                      <TableCell>{getStatusBadge(coupon)}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEdit(coupon)}>
                              <Edit className="mr-2 h-4 w-4" />
                              {t('actions.edit')}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDelete(coupon.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              {t('actions.delete')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-8">
              <Gift className="h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground">{t('empty.message')}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Coupon Modal */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{t('editModal.title')}</DialogTitle>
            <DialogDescription>{t('editModal.description')}</DialogDescription>
          </DialogHeader>

          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="couponCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.couponCode')}</FormLabel>
                      <div className="flex gap-2">
                        <FormControl>
                          <Input {...field} placeholder={t('form.couponCodePlaceholder')} />
                        </FormControl>
                        <Button type="button" variant="outline" onClick={generateEditCouponCode}>
                          {t('form.generate')}
                        </Button>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="discountType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.discountType')}</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Percentage">{t('discountType.percentage')}</SelectItem>
                          <SelectItem value="FixedAmount">{t('discountType.fixedAmount')}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="discountValue"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {form.watch('discountType') === 'Yüzde'
                          ? t('form.discountPercentage')
                          : t('form.discountAmount')
                        }</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0.01"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          placeholder={t('form.discountValuePlaceholder')}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="usageLimitPerCustomer"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.usageLimitPerCustomer')}</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                          placeholder={t('form.usageLimitPerCustomerPlaceholder')}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={editForm.control}
                name="expirationDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>{t('form.expirationDate')}</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>{t('form.selectDate')}</span>
                            )}
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date()}
                          autoFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>{t('form.expirationDateDescription')}</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.description')}</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder={t('form.descriptionPlaceholder')} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">{t('form.isActive')}</FormLabel>
                      <FormDescription>{t('form.isActiveDescription')}</FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  {t('actions.cancel')}
                </Button>
                <Button type="submit" disabled={updateCouponMutation.isPending}>
                  {updateCouponMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {t('actions.update')}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
