{"systemSettings": {"title": "System Settings", "single": "System Setting", "details": "System Setting Details", "list": "System Settings List", "add": "Add System Setting", "addDescription": "Create a new system setting", "edit": "Edit System Setting", "editDescription": "Edit existing system setting", "delete": "Delete System Setting", "search": "Search system settings...", "noSettings": "No system settings found", "groupedByCategory": "Grouped by Category", "uncategorized": "Uncategorized", "systemSettingsOnly": "System Settings Only", "userSettingsOnly": "User Settings Only", "fields": {"key": "Key", "value": "Value", "description": "Description", "category": "Category", "dataType": "Data Type", "isSystem": "System Setting", "isActive": "Active", "createdAt": "Created At", "updatedAt": "Updated At"}, "dataTypes": {"string": "String", "int": "Integer", "bool": "Boolean", "decimal": "Decimal"}, "categories": {"general": "General", "email": "Email", "points": "Points", "payment": "Payment", "shipping": "Shipping", "security": "Security", "notifications": "Notifications", "api": "API", "cache": "<PERSON><PERSON>", "logging": "Logging"}, "actions": {"addNew": "Add New Setting", "editInline": "Edit Inline", "saveChanges": "Save Changes", "cancelEdit": "Cancel Edit", "deleteConfirm": "Are you sure you want to delete this system setting?", "systemSettingCannotDelete": "System settings cannot be deleted", "expandCategory": "Expand Category", "collapseCategory": "Collapse Category", "showAll": "Show All", "hideAll": "Hide All"}, "messages": {"createSuccess": "System setting created successfully", "updateSuccess": "System setting updated successfully", "deleteSuccess": "System setting deleted successfully", "createError": "Error creating system setting", "updateError": "Error updating system setting", "deleteError": "Error deleting system setting", "keyExists": "This key already exists", "invalidValue": "Invalid value", "requiredField": "This field is required", "loadingSettings": "Loading system settings...", "noPermission": "You don't have permission for this action"}, "placeholders": {"key": "e.g. PointValidationDays", "value": "Enter setting value", "description": "Enter setting description", "category": "Select category or enter new category", "searchSettings": "Search by key, value or description..."}, "validation": {"keyRequired": "Key is required", "keyMinLength": "Key must be at least 1 character", "keyMaxLength": "Key must be at most 100 characters", "valueRequired": "Value is required", "valueMinLength": "Value must be at least 1 character", "valueMaxLength": "Value must be at most 30000 characters", "descriptionMaxLength": "Description must be at most 1000 characters", "categoryMaxLength": "Category must be at most 50 characters", "dataTypeRequired": "Data type is required", "invalidInteger": "Please enter a valid integer", "invalidDecimal": "Please enter a valid decimal number", "invalidBoolean": "Please enter true or false"}}}