{"systemSettings": {"title": "Sistem Ayarları", "single": "Sistem Ayarı", "details": "Sistem Ayarı Detayları", "list": "Sistem Ayarları Listesi", "add": "Sistem Ayarı Ekle", "addDescription": "Yeni sistem ayarı oluşturun", "edit": "Sistem Ayarı Düzenle", "editDescription": "Mevcut sistem ayarını düzenleyin", "delete": "Sistem Ayarı Sil", "search": "Sistem ayarı ara...", "noSettings": "Sistem ayarı bulunamadı", "groupedByCategory": "Kategoriye Göre Gruplandırılmış", "uncategorized": "Kategor<PERSON>z", "systemSettingsOnly": "<PERSON><PERSON><PERSON> Si<PERSON> A<PERSON>ları", "userSettingsOnly": "<PERSON><PERSON>e <PERSON>ıcı Ayarları", "fields": {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "dataType": "<PERSON><PERSON>", "isSystem": "Sistem Ayarı", "isActive": "Aktif", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dataTypes": {"string": "<PERSON><PERSON>", "int": "<PERSON><PERSON>", "bool": "Doğru/Yanlış", "decimal": "Ondalık <PERSON>"}, "categories": {"general": "<PERSON><PERSON>", "email": "E-posta", "points": "<PERSON><PERSON><PERSON>", "payment": "Ödeme", "shipping": "<PERSON><PERSON>", "security": "Güvenlik", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "api": "API", "cache": "Önbellek", "logging": "Günlük Kayıtları"}, "actions": {"addNew": "<PERSON><PERSON>", "editInline": "Satır İçi Düzenle", "saveChanges": "Değişiklikleri Kaydet", "cancelEdit": "Düzenlemeyi İptal", "deleteConfirm": "Bu sistem ayarını silmek istediğinizden emin misiniz?", "systemSettingCannotDelete": "Sistem ayarları silinemez", "expandCategory": "Kategoriyi Genişlet", "collapseCategory": "<PERSON><PERSON><PERSON><PERSON>", "showAll": "Tümünü <PERSON>ö<PERSON>", "hideAll": "Tümünü <PERSON>"}, "messages": {"createSuccess": "Sistem ayarı başarıyla oluşturuldu", "updateSuccess": "Sistem ayarı başarıyla güncellendi", "deleteSuccess": "Sistem ayarı baş<PERSON><PERSON><PERSON> silindi", "createError": "Sistem ayarı oluşturulurken hata oluştu", "updateError": "Sistem ayarı güncellenirken hata oluştu", "deleteError": "Sistem ayarı silinirken hata oluştu", "keyExists": "<PERSON>u anahtar zaten mevcut", "invalidValue": "Geç<PERSON><PERSON>", "requiredField": "<PERSON><PERSON> <PERSON><PERSON>", "loadingSettings": "Sistem ayarları yükleniyor...", "noPermission": "Bu işlem için <PERSON> yok"}, "placeholders": {"key": "Örn: PointValidationDays", "value": "<PERSON><PERSON>", "description": "<PERSON><PERSON> açıklamasını girin", "category": "<PERSON><PERSON><PERSON> se<PERSON>in veya yeni kategori girin", "searchSettings": "<PERSON><PERSON><PERSON>, de<PERSON>er veya açıklama ile ara..."}, "validation": {"keyRequired": "<PERSON><PERSON><PERSON>", "keyMinLength": "Anahtar en az 1 karakter olmalıdır", "keyMaxLength": "Anahtar en fazla 100 karakter olmalıdır", "valueRequired": "<PERSON><PERSON><PERSON>", "valueMinLength": "Değer en az 1 karakter olmalıdır", "valueMaxLength": "Değer en fazla 30000 karakter olmalıdır", "descriptionMaxLength": "Açıklama en fazla 1000 karakter olmalıdır", "categoryMaxLength": "Kategori en fazla 50 karakter olmalıdır", "dataTypeRequired": "Veri tipi z<PERSON>ludur", "invalidInteger": "Geçerli bir tam sayı girin", "invalidDecimal": "Geçerli bir ondalık sayı girin", "invalidBoolean": "true veya false de<PERSON><PERSON> girin"}}}