// SystemSettings types based on backend DTOs

export interface SystemSettingsDto {
  id: string;
  key: string;
  value: string;
  description?: string;
  category?: string;
  dataType: string;
  isSystem: boolean;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SystemSettingsCreateDto {
  key: string;
  value: string;
  description?: string;
  category?: string;
  dataType: string;
  isSystem: boolean;
}

export interface SystemSettingsUpdateDto {
  id: string;
  value: string;
  description?: string;
  category?: string;
  dataType: string;
}

export interface SystemSettingsListDto {
  id: string;
  key: string;
  value: string;
  description?: string;
  category?: string;
  dataType: string;
  isSystem: boolean;
  updatedAt: string;
}

// Filter parameters for API requests
export interface SystemSettingsFilterParams {
  category?: string;
  isSystem?: boolean;
  search?: string;
}

// Data types enum
export enum SystemSettingsDataType {
  STRING = 'string',
  INT = 'int',
  BOOL = 'bool',
  DECIMAL = 'decimal'
}

// Grouped settings by category
export interface GroupedSystemSettings {
  [category: string]: SystemSettingsDto[];
}

// Form data for creating/editing settings
export interface SystemSettingsFormData {
  key: string;
  value: string;
  description: string;
  category: string;
  dataType: SystemSettingsDataType;
  isSystem: boolean;
}

// API response types
export interface SystemSettingsResponse {
  data: SystemSettingsDto[];
  success: boolean;
  message?: string;
}

export interface SystemSettingResponse {
  data: SystemSettingsDto;
  success: boolean;
  message?: string;
}
