"use client";
import React from "react";
import Sidebar from "./Sidebar";

export default function AccountLayout({ children, title, breadcrumb }) {
  return (
    <>
      {/* Title Page */}
      <section className="tf-page-title">
        <div className="container">
          <div className="box-title text-center">
            <h4 className="title">{title || "Hesabım"}</h4>
            <div className="breadcrumb-list">
              <a className="breadcrumb-item" href="/">Anasayfa</a>
              <div className="breadcrumb-item dot"><span></span></div>
              <div className="breadcrumb-item current">{breadcrumb || title || "Hesabım"}</div>
            </div>
          </div>
        </div>
      </section>
      {/* /Title Page */}

      {/* Main Content */}
      <div className="flat-spacing-13">
        <div className="container-7">
          {/* sidebar-account */}
          <div className="btn-sidebar-mb d-lg-none">
            <button data-bs-toggle="offcanvas" data-bs-target="#mbAccount">
              <i className="icon icon-sidebar"></i>
            </button>
          </div>
          {/* /sidebar-account */}

          {/* Section-account */}
          <div className="main-content-account">
            <div className="sidebar-account-wrap sidebar-content-wrap sticky-top d-lg-block d-none">
              <ul className="my-account-nav">
                <Sidebar />
              </ul>
            </div>
            {children}
          </div>
          {/* /Account */}
        </div>
      </div>
      {/* /Main Content */}

      {/* sidebar account mobile */}
      <div className="offcanvas offcanvas-start canvas-sidebar" id="mbAccount">
        <div className="canvas-wrapper">
          <div className="canvas-header">
            <span className="title">HESAP MENÜSİ</span>
            <button className="icon-close icon-close-popup" data-bs-dismiss="offcanvas" aria-label="Close"></button>
          </div>
          <div className="canvas-body">
            <div className="sidebar-account-wrap sidebar-mobile-append">
              <ul className="my-account-nav">
                <Sidebar />
              </ul>
            </div>
          </div>
        </div>
      </div>
      {/* End sidebar account */}
    </>
  );
}
