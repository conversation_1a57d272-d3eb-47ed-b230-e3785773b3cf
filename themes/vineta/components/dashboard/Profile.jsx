"use client";
import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { customerRequests } from "@/services/account";
import AccountLayout from "./AccountLayout";

export default function Profile() {
  const { data: session } = useSession();
  const [customer, setCustomer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  // Form states
  const [profileForm, setProfileForm] = useState({
    id: '',
    nameSurname: '',
    phoneNumber: '',
    taxOrIdentityNumber: '',
    taxOffice: ''
  });

  const [passwordForm, setPasswordForm] = useState({
    customerId: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Load customer profile
  useEffect(() => {
    const loadProfile = async () => {
      try {
        const response = await customerRequests.getProfile();
        if (response) {
          const customerData = response;
          setCustomer(customerData);
          setProfileForm({
            id: customerData.id,
            nameSurname: customerData.nameSurname || '',
            phoneNumber: customerData.phoneNumber || '',
            taxOrIdentityNumber: customerData.taxOrIdentityNumber || '',
            taxOffice: customerData.taxOffice || ''
          });
          setPasswordForm(prev => ({ ...prev, customerId: customerData.id }));
        } else {
          setMessage({ type: 'error', text: response.data.message || 'Profil bilgileri yüklenemedi' });
        }
      } catch (error) {
        console.error('Profile load error:', error);
        setMessage({ type: 'error', text: 'Profil bilgileri yüklenirken bir hata oluştu' });
      } finally {
        setLoading(false);
      }
    };

    if (session?.accessToken) {
      loadProfile();
    }
  }, [session]);

  // Handle profile form changes
  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileForm(prev => ({ ...prev, [name]: value }));
  };

  // Handle password form changes
  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordForm(prev => ({ ...prev, [name]: value }));
  };

  // Update profile
  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    setUpdating(true);
    setMessage({ type: '', text: '' });

    try {
      const response = await customerRequests.updateProfile(profileForm);
      if (response) {
        setCustomer(response);
        setMessage({ type: 'success', text: 'Profil bilgileriniz başarıyla güncellendi' });
      } else {
        setMessage({ type: 'error', text: response.message || 'Profil güncellenemedi' });
      }
    } catch (error) {
      console.error('Profile update error:', error);
      setMessage({ type: 'error', text: 'Profil güncellenirken bir hata oluştu' });
    } finally {
      setUpdating(false);
    }
  };

  // Update password
  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    setUpdating(true);
    setMessage({ type: '', text: '' });

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setMessage({ type: 'error', text: 'Yeni şifre ve onay şifresi eşleşmiyor' });
      setUpdating(false);
      return;
    }

    try {
      const response = await customerRequests.updatePassword(passwordForm);
      if (response.data.isSuccessful) {
        setMessage({ type: 'success', text: 'Şifreniz başarıyla değiştirildi' });
        setPasswordForm({
          customerId: customer.id,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
        setShowPasswordForm(false);
      } else {
        setMessage({ type: 'error', text: response.data.message || 'Şifre değiştirilemedi' });
      }
    } catch (error) {
      console.error('Password update error:', error);
      setMessage({ type: 'error', text: 'Şifre değiştirilirken bir hata oluştu' });
    } finally {
      setUpdating(false);
    }
  };

  // Deactivate account
  const handleDeactivateAccount = async () => {
    setUpdating(true);
    setMessage({ type: '', text: '' });

    try {
      const response = await customerRequests.deactivateAccount({ customerId: customer.id });
      if (response.data.isSuccessful) {
        setMessage({ type: 'success', text: 'Hesabınız başarıyla pasifleştirildi' });
        setShowDeactivateModal(false);
        // Redirect to logout or home page after a delay
        setTimeout(() => {
          window.location.href = '/';
        }, 2000);
      } else {
        setMessage({ type: 'error', text: response.data.message || 'Hesap pasifleştirilemedi' });
      }
    } catch (error) {
      console.error('Account deactivation error:', error);
      setMessage({ type: 'error', text: 'Hesap pasifleştirilirken bir hata oluştu' });
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="my-acount-content account-dashboard">
        <div className="text-center">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Yükleniyor...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <AccountLayout title="Bilgilerim" breadcrumb="Bilgilerim">
      <div className="my-acount-content account-dashboard">
        <form action="#" className="form-edit-account">
          <h6 className="display-xs title-form">Hesap Bilgileri</h6>

          {/* Message Display */}
          {message.text && (
            <div className={`alert ${message.type === 'success' ? 'alert-success' : 'alert-danger'} mb-4`}>
              {message.text}
              <button
                type="button"
                className="btn-close"
                onClick={() => setMessage({ type: '', text: '' })}
              ></button>
            </div>
          )}

          {/* Personal Information Section */}
          <div className="form-name">
            <div className="tf-field style-2 style-3">
              <input
                className="tf-field-input tf-input"
                id="nameSurname"
                placeholder=" "
                type="text"
                name="nameSurname"
                value={profileForm.nameSurname}
                onChange={handleProfileChange}
                required
              />
              <label className="tf-field-label" htmlFor="nameSurname">Ad Soyad *</label>
            </div>

            <div className="tf-field style-2 style-3">
              <input
                className="tf-field-input tf-input"
                id="email"
                placeholder=" "
                type="email"
                value={customer?.email || ''}
                disabled
                style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed' }}
              />
              <label className="tf-field-label" htmlFor="email">E-posta (Değiştirilemez)</label>
            </div>

            <div className="tf-field style-2 style-3">
              <input
                className="tf-field-input tf-input"
                id="phoneNumber"
                placeholder=" "
                type="tel"
                name="phoneNumber"
                value={profileForm.phoneNumber}
                onChange={handleProfileChange}
              />
              <label className="tf-field-label" htmlFor="phoneNumber">Telefon Numarası</label>
            </div>

            <div className="tf-field style-2 style-3">
              <input
                className="tf-field-input tf-input"
                id="taxOrIdentityNumber"
                placeholder=" "
                type="text"
                name="taxOrIdentityNumber"
                value={profileForm.taxOrIdentityNumber}
                onChange={handleProfileChange}
              />
              <label className="tf-field-label" htmlFor="taxOrIdentityNumber">TC/Vergi No</label>
            </div>

            <div className="tf-field style-2 style-3">
              <input
                className="tf-field-input tf-input"
                id="taxOffice"
                placeholder=" "
                type="text"
                name="taxOffice"
                value={profileForm.taxOffice}
                onChange={handleProfileChange}
              />
              <label className="tf-field-label" htmlFor="taxOffice">Vergi Dairesi</label>
            </div>
            <button
              type="submit"
              className="tf-btn animate-btn mb-4"
              onClick={handleProfileSubmit}
              disabled={updating}
            >
              {updating ? 'Güncelleniyor...' : 'Bilgileri Güncelle'}
            </button>
          </div>


          {/* Password Change Section */}
          <div className="form-pass">
            <>
              <div className="tf-field style-2 style-3">
                <input
                  className="tf-field-input tf-input"
                  id="currentPassword"
                  placeholder=" "
                  type="password"
                  name="currentPassword"
                  value={passwordForm.currentPassword}
                  onChange={handlePasswordChange}
                  required
                />
                <label className="tf-field-label" htmlFor="currentPassword">Mevcut Şifre *</label>
              </div>

              <div className="tf-field style-2 style-3">
                <input
                  className="tf-field-input tf-input"
                  id="newPassword"
                  placeholder=" "
                  type="password"
                  name="newPassword"
                  value={passwordForm.newPassword}
                  onChange={handlePasswordChange}
                  minLength="6"
                  required
                />
                <label className="tf-field-label" htmlFor="newPassword">Yeni Şifre *</label>
              </div>

              <div className="tf-field style-2 style-3">
                <input
                  className="tf-field-input tf-input"
                  id="confirmPassword"
                  placeholder=" "
                  type="password"
                  name="confirmPassword"
                  value={passwordForm.confirmPassword}
                  onChange={handlePasswordChange}
                  minLength="6"
                  required
                />
                <label className="tf-field-label" htmlFor="confirmPassword">Yeni Şifre Onay *</label>
              </div>

              <button
                type="button"
                className="tf-btn animate-btn mb-4"
                onClick={handlePasswordSubmit}
                disabled={updating}
              >
                {updating ? 'Değiştiriliyor...' : 'Şifreyi Değiştir'}
              </button>
            </>
          </div>

          {/* Account Deactivation Section */}
          <div className="form-deactivate">
            <div className="text-lg title-pass text-danger mb-3">Hesap İşlemleri</div>
            <p className="text-muted mb-3">
              Hesabınızı pasifleştirmek istiyorsanız aşağıdaki butonu kullanabilirsiniz.
              Bu işlem geri alınamaz ve hesabınıza erişiminiz kısıtlanacaktır.
            </p>
            <button
              type="button"
              className="tf-btn btn-out-line-dark2"
              onClick={() => setShowDeactivateModal(true)}
            >
              Hesabımı Pasifleştir
            </button>
          </div>
        </form>

        {/* Deactivation Confirmation Modal */}
        {showDeactivateModal && (
          <div className="modal fade show" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.5)' }}>
            <div className="modal-dialog modal-dialog-centered">
              <div className="modal-content">
                <div className="header">
                  <h5 className="demo-title">Hesap Pasifleştirme Onayı</h5>
                  <span
                    className="icon-close icon-close-popup"
                    onClick={() => setShowDeactivateModal(false)}
                    style={{ cursor: 'pointer' }}
                  ></span>
                </div>
                <div className="modal-body p-4">
                  <p className="mb-3">Hesabınızı pasifleştirmek istediğinizden emin misiniz?</p>
                  <p className="text-danger mb-0">
                    <strong>Uyarı:</strong> Bu işlem geri alınamaz ve hesabınıza erişiminiz kısıtlanacaktır.
                  </p>
                </div>
                <div className="modal-footer p-4">
                  <button
                    type="button"
                    className="tf-btn btn-out-line-dark2 me-2"
                    onClick={() => setShowDeactivateModal(false)}
                  >
                    İptal
                  </button>
                  <button
                    type="button"
                    className="tf-btn animate-btn bg-danger"
                    onClick={handleDeactivateAccount}
                    disabled={updating}
                  >
                    {updating ? 'Pasifleştiriliyor...' : 'Evet, Pasifleştir'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AccountLayout>
  );
}
