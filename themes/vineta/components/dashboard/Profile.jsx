"use client";
import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { customerRequests } from "@/services/account";

export default function Profile() {
  const { data: session } = useSession();
  const [customer, setCustomer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  // Form states
  const [profileForm, setProfileForm] = useState({
    id: '',
    nameSurname: '',
    phoneNumber: '',
    taxOrIdentityNumber: '',
    taxOffice: ''
  });

  const [passwordForm, setPasswordForm] = useState({
    customerId: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Load customer profile
  useEffect(() => {
    const loadProfile = async () => {
      try {
        const response = await customerRequests.getProfile();
        if (response.data.isSuccessful) {
          const customerData = response.data.customer;
          setCustomer(customerData);
          setProfileForm({
            id: customerData.id,
            nameSurname: customerData.nameSurname || '',
            phoneNumber: customerData.phoneNumber || '',
            taxOrIdentityNumber: customerData.taxOrIdentityNumber || '',
            taxOffice: customerData.taxOffice || ''
          });
          setPasswordForm(prev => ({ ...prev, customerId: customerData.id }));
        } else {
          setMessage({ type: 'error', text: response.data.message || 'Profil bilgileri yüklenemedi' });
        }
      } catch (error) {
        console.error('Profile load error:', error);
        setMessage({ type: 'error', text: 'Profil bilgileri yüklenirken bir hata oluştu' });
      } finally {
        setLoading(false);
      }
    };

    if (session?.accessToken) {
      loadProfile();
    }
  }, [session]);

  // Handle profile form changes
  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileForm(prev => ({ ...prev, [name]: value }));
  };

  // Handle password form changes
  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordForm(prev => ({ ...prev, [name]: value }));
  };

  // Update profile
  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    setUpdating(true);
    setMessage({ type: '', text: '' });

    try {
      const response = await customerRequests.updateProfile(profileForm);
      if (response.data.isSuccessful) {
        setCustomer(response.data.customer);
        setMessage({ type: 'success', text: 'Profil bilgileriniz başarıyla güncellendi' });
      } else {
        setMessage({ type: 'error', text: response.data.message || 'Profil güncellenemedi' });
      }
    } catch (error) {
      console.error('Profile update error:', error);
      setMessage({ type: 'error', text: 'Profil güncellenirken bir hata oluştu' });
    } finally {
      setUpdating(false);
    }
  };

  // Update password
  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    setUpdating(true);
    setMessage({ type: '', text: '' });

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setMessage({ type: 'error', text: 'Yeni şifre ve onay şifresi eşleşmiyor' });
      setUpdating(false);
      return;
    }

    try {
      const response = await customerRequests.updatePassword(passwordForm);
      if (response.data.isSuccessful) {
        setMessage({ type: 'success', text: 'Şifreniz başarıyla değiştirildi' });
        setPasswordForm({
          customerId: customer.id,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
        setShowPasswordForm(false);
      } else {
        setMessage({ type: 'error', text: response.data.message || 'Şifre değiştirilemedi' });
      }
    } catch (error) {
      console.error('Password update error:', error);
      setMessage({ type: 'error', text: 'Şifre değiştirilirken bir hata oluştu' });
    } finally {
      setUpdating(false);
    }
  };

  // Deactivate account
  const handleDeactivateAccount = async () => {
    setUpdating(true);
    setMessage({ type: '', text: '' });

    try {
      const response = await customerRequests.deactivateAccount({ customerId: customer.id });
      if (response.data.isSuccessful) {
        setMessage({ type: 'success', text: 'Hesabınız başarıyla pasifleştirildi' });
        setShowDeactivateModal(false);
        // Redirect to logout or home page after a delay
        setTimeout(() => {
          window.location.href = '/';
        }, 2000);
      } else {
        setMessage({ type: 'error', text: response.data.message || 'Hesap pasifleştirilemedi' });
      }
    } catch (error) {
      console.error('Account deactivation error:', error);
      setMessage({ type: 'error', text: 'Hesap pasifleştirilirken bir hata oluştu' });
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="my-acount-content">
        <div className="text-center py-5">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Yükleniyor...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="my-acount-content">
      <div className="box-account-title">
        <h4 className="display-sm fw-medium">Bilgilerim</h4>
        <p className="notice text-sm">
          Hesabınıza ait kişisel bilgilerinizi buradan görüntüleyebilir ve güncelleyebilirsiniz.
        </p>
      </div>

      {/* Message Display */}
      {message.text && (
        <div className={`alert ${message.type === 'success' ? 'alert-success' : 'alert-danger'} alert-dismissible fade show`} role="alert">
          {message.text}
          <button type="button" className="btn-close" onClick={() => setMessage({ type: '', text: '' })}></button>
        </div>
      )}

      <div className="content-account">
        {/* Personal Information Section */}
        <div className="card mb-4">
          <div className="card-header">
            <h5 className="mb-0">Kişisel Bilgiler</h5>
          </div>
          <div className="card-body">
            <form onSubmit={handleProfileSubmit}>
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label htmlFor="nameSurname" className="form-label">Ad Soyad *</label>
                  <input
                    type="text"
                    className="form-control"
                    id="nameSurname"
                    name="nameSurname"
                    value={profileForm.nameSurname}
                    onChange={handleProfileChange}
                    required
                  />
                </div>
                <div className="col-md-6 mb-3">
                  <label htmlFor="email" className="form-label">E-posta</label>
                  <input
                    type="email"
                    className="form-control"
                    id="email"
                    value={customer?.email || ''}
                    disabled
                    style={{ backgroundColor: '#f8f9fa' }}
                  />
                  <small className="text-muted">E-posta adresi değiştirilemez</small>
                </div>
              </div>
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label htmlFor="phoneNumber" className="form-label">Telefon Numarası</label>
                  <input
                    type="tel"
                    className="form-control"
                    id="phoneNumber"
                    name="phoneNumber"
                    value={profileForm.phoneNumber}
                    onChange={handleProfileChange}
                  />
                </div>
                <div className="col-md-6 mb-3">
                  <label htmlFor="taxOrIdentityNumber" className="form-label">TC/Vergi No</label>
                  <input
                    type="text"
                    className="form-control"
                    id="taxOrIdentityNumber"
                    name="taxOrIdentityNumber"
                    value={profileForm.taxOrIdentityNumber}
                    onChange={handleProfileChange}
                  />
                </div>
              </div>
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label htmlFor="taxOffice" className="form-label">Vergi Dairesi</label>
                  <input
                    type="text"
                    className="form-control"
                    id="taxOffice"
                    name="taxOffice"
                    value={profileForm.taxOffice}
                    onChange={handleProfileChange}
                  />
                </div>
              </div>
              <button type="submit" className="btn btn-primary" disabled={updating}>
                {updating ? 'Güncelleniyor...' : 'Bilgileri Güncelle'}
              </button>
            </form>
          </div>
        </div>

        {/* Password Change Section */}
        <div className="card mb-4">
          <div className="card-header d-flex justify-content-between align-items-center">
            <h5 className="mb-0">Şifre Değiştir</h5>
            <button
              type="button"
              className="btn btn-outline-primary btn-sm"
              onClick={() => setShowPasswordForm(!showPasswordForm)}
            >
              {showPasswordForm ? 'İptal' : 'Şifre Değiştir'}
            </button>
          </div>
          {showPasswordForm && (
            <div className="card-body">
              <form onSubmit={handlePasswordSubmit}>
                <div className="row">
                  <div className="col-md-4 mb-3">
                    <label htmlFor="currentPassword" className="form-label">Mevcut Şifre *</label>
                    <input
                      type="password"
                      className="form-control"
                      id="currentPassword"
                      name="currentPassword"
                      value={passwordForm.currentPassword}
                      onChange={handlePasswordChange}
                      required
                    />
                  </div>
                  <div className="col-md-4 mb-3">
                    <label htmlFor="newPassword" className="form-label">Yeni Şifre *</label>
                    <input
                      type="password"
                      className="form-control"
                      id="newPassword"
                      name="newPassword"
                      value={passwordForm.newPassword}
                      onChange={handlePasswordChange}
                      minLength="6"
                      required
                    />
                  </div>
                  <div className="col-md-4 mb-3">
                    <label htmlFor="confirmPassword" className="form-label">Yeni Şifre Onay *</label>
                    <input
                      type="password"
                      className="form-control"
                      id="confirmPassword"
                      name="confirmPassword"
                      value={passwordForm.confirmPassword}
                      onChange={handlePasswordChange}
                      minLength="6"
                      required
                    />
                  </div>
                </div>
                <button type="submit" className="btn btn-warning" disabled={updating}>
                  {updating ? 'Değiştiriliyor...' : 'Şifreyi Değiştir'}
                </button>
              </form>
            </div>
          )}
        </div>

        {/* Account Deactivation Section */}
        <div className="card mb-4">
          <div className="card-header">
            <h5 className="mb-0 text-danger">Hesap İşlemleri</h5>
          </div>
          <div className="card-body">
            <p className="text-muted mb-3">
              Hesabınızı pasifleştirmek istiyorsanız aşağıdaki butonu kullanabilirsiniz. 
              Bu işlem geri alınamaz ve hesabınıza erişiminiz kısıtlanacaktır.
            </p>
            <button
              type="button"
              className="btn btn-danger"
              onClick={() => setShowDeactivateModal(true)}
            >
              Hesabımı Pasifleştir
            </button>
          </div>
        </div>
      </div>

      {/* Deactivation Confirmation Modal */}
      {showDeactivateModal && (
        <div className="modal fade show" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Hesap Pasifleştirme Onayı</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowDeactivateModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <p>Hesabınızı pasifleştirmek istediğinizden emin misiniz?</p>
                <p className="text-danger">
                  <strong>Uyarı:</strong> Bu işlem geri alınamaz ve hesabınıza erişiminiz kısıtlanacaktır.
                </p>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowDeactivateModal(false)}
                >
                  İptal
                </button>
                <button
                  type="button"
                  className="btn btn-danger"
                  onClick={handleDeactivateAccount}
                  disabled={updating}
                >
                  {updating ? 'Pasifleştiriliyor...' : 'Evet, Pasifleştir'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
