"use client";
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

const FilterContext = createContext();

export const useFilter = () => {
  const context = useContext(FilterContext);
  if (!context) {
    throw new Error('useFilter must be used within a FilterProvider');
  }
  return context;
};

// Default filter state - moved outside component to prevent recreation
const defaultFilters = {
  page: 1,
  limit: 12,
  sort: 'default',
  categories: [],
  brands: [],
  sizes: [],
  variantAttributes: {},
  priceMin: null,
  priceMax: null,
  inStock: null,
  search: ''
};

export const FilterProvider = ({ children }) => {
  const [filters, setFilters] = useState(defaultFilters);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load filters from localStorage on mount
  useEffect(() => {
    try {
      const savedFilters = localStorage.getItem('productFilters');
      if (savedFilters) {
        const parsedFilters = JSON.parse(savedFilters);
        // Merge with defaults to ensure all properties exist
        setFilters({ ...defaultFilters, ...parsedFilters });
      }
    } catch (error) {
      console.error('Error loading filters from localStorage:', error);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save filters to localStorage whenever they change
  useEffect(() => {
    if (isLoaded) {
      try {
        // Only save non-default values to keep localStorage clean
        const filtersToSave = {};
        Object.keys(filters).forEach(key => {
          const value = filters[key];
          const defaultValue = defaultFilters[key];

          // Save if value is different from default
          if (key === 'page' && value !== 1) filtersToSave[key] = value;
          else if (key === 'limit' && value !== 12) filtersToSave[key] = value;
          else if (key === 'sort' && value !== 'default') filtersToSave[key] = value;
          else if (Array.isArray(value) && value.length > 0) filtersToSave[key] = value;
          else if (key === 'variantAttributes' && Object.keys(value || {}).length > 0) filtersToSave[key] = value;
          else if ((key === 'priceMin' || key === 'priceMax') && value !== null) filtersToSave[key] = value;
          else if (key === 'in_stock' && value !== null) filtersToSave[key] = value;
          else if (key === 'search' && value !== '') filtersToSave[key] = value;
        });

        if (Object.keys(filtersToSave).length > 0) {
          localStorage.setItem('productFilters', JSON.stringify(filtersToSave));

        } else {
          localStorage.removeItem('productFilters');
        }
      } catch (error) {
        console.error('Error saving filters to localStorage:', error);
      }
    }
  }, [filters, isLoaded]);

  // Update specific filter
  const updateFilter = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value // Reset page when other filters change
    }));
  };

  // Update multiple filters at once
  const updateFilters = (newFilters) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
  };

  // Validate filters against available options
  const validateFilters = useCallback((currentFilters, availableOptions) => {
    if (!availableOptions) return currentFilters;

    const validatedFilters = { ...currentFilters };
    let hasChanges = false;

    // Validate categories
    if (availableOptions.categories && validatedFilters.categories.length > 0) {
      const validCategories = validatedFilters.categories.filter(catId =>
        availableOptions.categories.some(cat => cat.id === catId || cat.slug === catId)
      );
      if (validCategories.length !== validatedFilters.categories.length) {
        validatedFilters.categories = validCategories;
        hasChanges = true;
      }
    }

    // Validate brands
    if (availableOptions.brands && validatedFilters.brands.length > 0) {
      const validBrands = validatedFilters.brands.filter(brandName =>
        availableOptions.brands.some(brand => brand.name === brandName)
      );
      if (validBrands.length !== validatedFilters.brands.length) {
        validatedFilters.brands = validBrands;
        hasChanges = true;
      }
    }

    // Validate variant attributes
    if (availableOptions.variantAttributes && Object.keys(validatedFilters.variantAttributes || {}).length > 0) {
      const validVariantAttributes = {};
      Object.entries(validatedFilters.variantAttributes || {}).forEach(([attrName, values]) => {
        const availableAttr = availableOptions.variantAttributes.find(attr => attr.shortName === attrName);
        if (availableAttr) {
          const validValues = values.filter(value =>
            availableAttr.values.some(attrValue => attrValue.value === value)
          );
          if (validValues.length > 0) {
            validVariantAttributes[attrName] = validValues;
          }
          if (validValues.length !== values.length) {
            hasChanges = true;
          }
        } else {
          hasChanges = true;
        }
      });
      validatedFilters.variantAttributes = validVariantAttributes;
    }

    // Validate price range
    if (availableOptions.priceRange) {
      const { minPrice, maxPrice } = availableOptions.priceRange;
      if (validatedFilters.priceMin !== null && validatedFilters.priceMin < minPrice) {
        validatedFilters.priceMin = minPrice;
        hasChanges = true;
      }
      if (validatedFilters.priceMax !== null && validatedFilters.priceMax > maxPrice) {
        validatedFilters.priceMax = maxPrice;
        hasChanges = true;
      }
    }

    return { validatedFilters, hasChanges };
  }, []);

  // Clear all filters
  const clearFilters = () => {
    setFilters(defaultFilters);
  };

  // Clear specific filter
  const clearFilter = (key) => {
    setFilters(prev => ({
      ...prev,
      [key]: defaultFilters[key],
      page: 1
    }));
  };

  // Check if any filters are active
  const hasActiveFilters = () => {
    return filters.categories.length > 0 ||
      filters.brands.length > 0 ||
      filters.sizes.length > 0 ||
      Object.keys(filters.variantAttributes || {}).length > 0 ||
      filters.priceMin !== null ||
      filters.priceMax !== null ||
      filters.inStock !== null ||
      filters.search !== '';
  };

  // Get filter count for display
  const getActiveFilterCount = () => {
    let count = 0;
    count += filters.categories.length;
    count += filters.brands.length;
    count += filters.sizes.length;
    count += Object.values(filters.variantAttributes || {}).reduce((acc, arr) => acc + arr.length, 0);
    if (filters.priceMin !== null || filters.priceMax !== null) count++;
    if (filters.inStock !== null) count++;
    if (filters.search !== '') count++;
    return count;
  };

  // Build URL params from filters
  const buildUrlParams = useCallback(() => {
    const params = new URLSearchParams();

    if (filters.page > 1) params.set('page', filters.page.toString());
    if (filters.limit !== 12) params.set('limit', filters.limit.toString());
    if (filters.sort !== 'default') params.set('sort', filters.sort);
    if (filters.categories.length) params.set('categories', filters.categories.join(','));
    if (filters.brands.length) params.set('brands', filters.brands.join(','));
    if (filters.sizes.length) params.set('sizes', filters.sizes.join(','));

    // Handle variant attributes
    if (filters.variantAttributes) {
      Object.entries(filters.variantAttributes).forEach(([attrName, values]) => {
        if (values.length > 0) {
          params.set(`attr_${attrName}`, values.join(','));
        }
      });
    }

    if (filters.priceMin) params.set('price_min', filters.priceMin.toString());
    if (filters.priceMax) params.set('price_max', filters.priceMax.toString());
    if (filters.inStock !== null) params.set('in_stock', filters.inStock.toString());
    if (filters.search) params.set('search', filters.search);

    return params;
  }, [filters]);

  // Parse URL params to filters
  const parseUrlParams = useCallback((searchParams) => {
    const parseVariantAttributes = () => {
      const variantAttrs = {};
      for (const [key, value] of searchParams.entries()) {
        if (key.startsWith('attr_')) {
          const attrName = key.replace('attr_', '');
          variantAttrs[attrName] = value.split(',').filter(Boolean);
        }
      }
      return variantAttrs;
    };

    return {
      page: parseInt(searchParams.get('page')) || 1,
      limit: parseInt(searchParams.get('limit')) || 12,
      sort: searchParams.get('sort') || 'default',
      categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
      brands: searchParams.get('brands')?.split(',').filter(Boolean) || [],
      sizes: searchParams.get('sizes')?.split(',').filter(Boolean) || [],
      variantAttributes: parseVariantAttributes(),
      priceMin: parseFloat(searchParams.get('price_min')) || null,
      priceMax: parseFloat(searchParams.get('price_max')) || null,
      inStock: searchParams.get('in_stock') === 'true' ? true : searchParams.get('in_stock') === 'false' ? false : null,
      search: searchParams.get('search') || ''
    };
  }, []);

  // Validate and update filters with available options
  const validateAndUpdateFilters = useCallback((availableOptions) => {
    const { validatedFilters, hasChanges } = validateFilters(filters, availableOptions);
    if (hasChanges) {
      setFilters(validatedFilters);
      return validatedFilters;
    }
    return filters;
  }, [filters, validateFilters]);

  // Validate filters on initial load with available options
  const validateFiltersOnLoad = useCallback((availableOptions) => {
    if (!isLoaded || !availableOptions) return;

    const { validatedFilters, hasChanges } = validateFilters(filters, availableOptions);
    if (hasChanges) {
      setFilters(validatedFilters);
    }
  }, [filters, validateFilters, isLoaded]);

  const value = {
    filters,
    updateFilter,
    updateFilters,
    clearFilters,
    clearFilter,
    hasActiveFilters,
    getActiveFilterCount,
    buildUrlParams,
    parseUrlParams,
    validateAndUpdateFilters,
    validateFiltersOnLoad,
    isLoaded
  };

  return (
    <FilterContext.Provider value={value}>
      {children}
    </FilterContext.Provider>
  );
};
