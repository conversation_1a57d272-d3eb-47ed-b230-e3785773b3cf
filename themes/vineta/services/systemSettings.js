import { api } from '@/lib/api/client';
import { serverApi } from '@/lib/api/server';

/**
 * B2C Customer System Settings API Services
 * Sistem ayarlarını çekmek için servisler
 */

// Belirli bir anahtar ile sistem ayarını getir
export const getSystemSettingByKey = async (key) => {
  try {
    const response = await api.get(`/systemsettings/by-key/${key}`);
    // WebApi ApiResponse wrapper'ını handle et
    return response.data || response;
  } catch (error) {
    console.error(`Error fetching system setting with key ${key}:`, error);
    return null;
  }
};

// Server-side rendering için belirli bir anahtar ile sistem ayarını getir
export const getSystemSettingByKeySSR = async (key) => {
  try {
    const response = await serverApi.get(`/systemsettings/by-key/${key}`);
    // WebApi ApiResponse wrapper'ını handle et
    return response.data || response;
  } catch (error) {
    console.error(`Error fetching system setting with key ${key} (SSR):`, error);
    return null;
  }
};

// Tüm sistem ayarlarını getir
export const getSystemSettings = async () => {
  try {
    const response = await api.get('/systemsettings');
    // WebApi ApiResponse wrapper'ını handle et
    return response.data || response || [];
  } catch (error) {
    console.error('Error fetching system settings:', error);
    return [];
  }
};

// Server-side rendering için tüm sistem ayarlarını getir
export const getSystemSettingsSSR = async () => {
  try {
    const response = await serverApi.get('/systemsettings');
    // WebApi ApiResponse wrapper'ını handle et
    return response.data || response || [];
  } catch (error) {
    console.error('Error fetching system settings (SSR):', error);
    return [];
  }
};

// Kategoriye göre sistem ayarlarını getir
export const getSystemSettingsByCategory = async (category) => {
  try {
    const response = await api.get(`/systemsettings/by-category/${category}`);
    // WebApi ApiResponse wrapper'ını handle et
    return response.data || response || [];
  } catch (error) {
    console.error(`Error fetching system settings for category ${category}:`, error);
    return [];
  }
};

// Server-side rendering için kategoriye göre sistem ayarlarını getir
export const getSystemSettingsByCategorySSR = async (category) => {
  try {
    const response = await serverApi.get(`/systemsettings/by-category/${category}`);
    // WebApi ApiResponse wrapper'ını handle et
    return response.data || response || [];
  } catch (error) {
    console.error(`Error fetching system settings for category ${category} (SSR):`, error);
    return [];
  }
};

// KayanYazi ayarını getir ve virgülle ayrılmış string'i array'e çevir
export const getMarqueeTexts = async () => {
  try {
    const setting = await getSystemSettingByKey('KayanYazi');
    if (setting && setting.value) {
      // Virgülle ayrılmış string'i array'e çevir ve boş elemanları filtrele
      return setting.value.split(',').map(text => text.trim()).filter(text => text.length > 0);
    }
    return [];
  } catch (error) {
    console.error('Error fetching marquee texts:', error);
    // Fallback olarak default değerleri döndür
    return ["14 Gün İade Süresi", "1000 TL Üzeri Bedava Kargo", "Hassas Ciltler İçin Rahatlama"];
  }
};

// Server-side rendering için KayanYazi ayarını getir
export const getMarqueeTextsSSR = async () => {
  try {
    const setting = await getSystemSettingByKeySSR('KayanYazi');
    if (setting && setting.value) {
      // Virgülle ayrılmış string'i array'e çevir ve boş elemanları filtrele
      return setting.value.split(',').map(text => text.trim()).filter(text => text.length > 0);
    }
    return [];
  } catch (error) {
    console.error('Error fetching marquee texts (SSR):', error);
    // Fallback olarak default değerleri döndür
    return ["14 Gün İade Süresi", "1000 TL Üzeri Bedava Kargo", "Hassas Ciltler İçin Rahatlama"];
  }
};
