import { useState, useEffect } from 'react';
import { api } from '@/lib/api/client';
import { useCustomerAuth } from '@/hooks/useCustomerAuth';

export const useCustomerCoupons = () => {
  const { session, isAuthenticated } = useCustomerAuth();
  const [coupons, setCoupons] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchAvailableCoupons = async () => {
    if (!isAuthenticated || !session?.customer?.id) {
      setCoupons([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await api.get('/coupon/available');
      setCoupons(response || []);
    } catch (err) {
      setError(err.message);
      setCoupons([]);
    } finally {
      setLoading(false);
    }
  };

  const validateCoupon = async (couponCode, orderAmount) => {
    if (!isAuthenticated || !session?.customer?.id) {
      throw new Error('Kullanıcı girişi gerekli');
    }

    try {
      const response = await api.post('/coupon/validate', {
        couponCode,
        customerId: session.customer.id,
        orderAmount
      });
      return response;
    } catch (err) {
      throw new Error(err.message || 'Kupon doğrulaması başarısız');
    }
  };

  useEffect(() => {
    fetchAvailableCoupons();
  }, [isAuthenticated, session?.customer?.id]);

  return {
    coupons,
    loading,
    error,
    refetch: fetchAvailableCoupons,
    validateCoupon
  };
};
